namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface INodrizasRepo : IRepository<Nodrizas>
{
    Task<Nodrizas?> GetByIdAsync(int id, bool asNoTracking = true, CancellationToken cancellationToken = default);
    Task<Nodrizas?> GetByNumNodrizaAsync(int numNodriza, bool asNoTracking = true, CancellationToken cancellationToken = default);
}