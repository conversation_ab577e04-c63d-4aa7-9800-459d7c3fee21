namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface INodrizasRepo
{
    // Métodos específicos de dominio
    Task<Nodrizas?> GetByIdAsync(int id, NodrizasQueryOptions options, CancellationToken cancellationToken = default);

    // Métodos genéricos
    Task<List<Nodrizas>> GetAllAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default);
    Task<List<Nodrizas>> GetActivasAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default);
    Task<int> CountAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default);
    Task<int> CountActivasAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default);
}