﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Options;

public interface INodrizasRepo : IRepository<Nodrizas>
{
    Task<Nodrizas?> GetByIdAsync(int id, bool asNoTracking = true, CancellationToken cancellationToken = default);
    Task<Nodrizas?> GetByNumNodrizaAsync(int numNodriza, bool asNoTracking = true, CancellationToken cancellationToken = default);

    // Métodos con Options para navigation properties
    Task<Nodrizas?> GetByIdAsync(int id, NodrizaQueryOptions? options = null, CancellationToken cancellationToken = default);
    Task<Nodrizas?> GetByNumNodrizaAsync(int numNodriza, NodrizaQueryOptions? options = null, CancellationToken cancellationToken = default);

    // Métodos de conveniencia para casos comunes
    Task<Nodrizas?> GetByIdWithLotesAsync(int id, CancellationToken cancellationToken = default);
    Task<Nodrizas?> GetByNumNodrizaWithLotesAsync(int numNodriza, CancellationToken cancellationToken = default);
}