﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface INodrizasRepo : IRepository<Nodrizas>
{
    Task<Nodrizas?> GetByIdAsync(int id, NodrizasQueryOptions options, CancellationToken cancellationToken = default);
    Task<Nodrizas?> GetByNumNodrizaAsync(int numNodriza, NodrizasQueryOptions options, CancellationToken cancellationToken = default);
    Task<List<Nodrizas>> GetActivasAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default);
}