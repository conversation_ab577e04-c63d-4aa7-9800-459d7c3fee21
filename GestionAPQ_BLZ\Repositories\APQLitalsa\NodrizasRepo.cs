namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;
using Options;

public class NodrizasRepo : Repository<Nodrizas, APQLitalsaContext>, INodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public NodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<Nodrizas?> GetByIdAsync(int id, bool asNoTracking = true, CancellationToken cancellationToken = default)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken, n => n.Id == id && !n.<PERSON>);
    }

    public async Task<Nodrizas?> GetByNumNodrizaAsync(int numNodriza, bool asNoTracking = true, CancellationToken cancellationToken = default)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken, n => n.NumNodriza == numNodriza && !n.Borrada);
    }

    // Métodos con Options para navigation properties
    public async Task<Nodrizas?> GetByIdAsync(int id, NodrizaQueryOptions? options = null, CancellationToken cancellationToken = default)
    {
        options ??= new NodrizaQueryOptions();

        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (options.IncludeLotes)
            query = query.Include(n => n.LotesNodrizas);

        return await query
            .Where(n => n.Id == id && !n.Borrada)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<Nodrizas?> GetByNumNodrizaAsync(int numNodriza, NodrizaQueryOptions? options = null, CancellationToken cancellationToken = default)
    {
        options ??= new NodrizaQueryOptions();

        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (options.IncludeLotes)
            query = query.Include(n => n.LotesNodrizas);

        return await query
            .Where(n => n.NumNodriza == numNodriza && !n.Borrada)
            .FirstOrDefaultAsync(cancellationToken);
    }

    // Métodos de conveniencia para casos comunes
    public async Task<Nodrizas?> GetByIdWithLotesAsync(int id, CancellationToken cancellationToken = default)
    {
        return await GetByIdAsync(id, new NodrizaQueryOptions { IncludeLotes = true }, cancellationToken);
    }

    public async Task<Nodrizas?> GetByNumNodrizaWithLotesAsync(int numNodriza, CancellationToken cancellationToken = default)
    {
        return await GetByNumNodrizaAsync(numNodriza, new NodrizaQueryOptions { IncludeLotes = true }, cancellationToken);
    }
}