namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base.APQLitalsa;
using Extensions;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class NodrizasRepo : INodrizasRepo
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<Nodrizas> _dbSet;

    public NodrizasRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<Nodrizas>();
    }

    public async Task<Nodrizas?> GetByIdAsync(int id, NodrizasQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        return await _dbSet.GetFirstOrDefaultAsync(
            n => n.Id == id && !n.<PERSON>da,
            options,
            cancellationToken);
    }

    // Métodos genéricos usando extensions
    public async Task<List<Nodrizas>> GetAllAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.GetWhereAsync(n => !n.Borrada, options, cancellationToken);
    }

    public async Task<List<Nodrizas>> GetActivasAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.GetWhereAsync(n => n.Activo && !n.Borrada, options, cancellationToken);
    }

    public async Task<int> CountAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(n => !n.Borrada, options, cancellationToken);
    }

    public async Task<int> CountActivasAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(n => n.Activo && !n.Borrada, options, cancellationToken);
    }
}