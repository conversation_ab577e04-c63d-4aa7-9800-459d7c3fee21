namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class NodrizasRepo : Repository<Nodrizas, APQLitalsaContext>, INodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public NodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<Nodrizas?> GetByIdAsync(int id, bool asNoTracking = true, CancellationToken cancellationToken = default)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken, n => n.Id == id && !n.<PERSON>);
    }

    public async Task<Nodrizas?> GetByNumNodrizaAsync(int numNodriza, bool asNoTracking = true, CancellationToken cancellationToken = default)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken, n => n.NumNodriza == numNodriza && !n.Borrada);
    }
}