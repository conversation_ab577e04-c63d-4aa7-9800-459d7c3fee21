﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class NodrizasRepo : Repository<Nodrizas, APQLitalsaContext>, INodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public NodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<Nodrizas?> GetByIdAsync(int id, NodrizasQueryOptions options, CancellationToken cancellationToken = default)
    {
        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (options.IncludeLotes)
            query = query.Include(n => n.LotesNodrizas);

        return await query
            .Where(n => n.Id == id && !n.<PERSON>)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<Nodrizas?> GetByNumNodrizaAsync(int numNodriza, NodrizasQueryOptions options, CancellationToken cancellationToken = default)
    {
        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (options.IncludeLotes)
            query = query.Include(n => n.LotesNodrizas);

        return await query
            .Where(n => n.NumNodriza == numNodriza && !n.Borrada)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<Nodrizas>> GetActivasAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default)
    {
        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (options.IncludeLotes)
            query = query.Include(n => n.LotesNodrizas);

        return await query
            .Where(n => n.Activo && !n.Borrada)
            .ToListAsync(cancellationToken);
    }
}