﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class NodrizasRepo : INodrizasRepo
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<Nodrizas> _dbSet;

    public NodrizasRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<Nodrizas>();
    }

    public async Task<Nodrizas?> GetByIdAsync(int id, NodrizasQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.Set(_dbSet);

        return await query
            .Where(n => n.Id == id && !n.<PERSON>)
            .FirstOrDefaultAsync(cancellationToken);
    }
}