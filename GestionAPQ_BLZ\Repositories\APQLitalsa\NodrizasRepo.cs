﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using Extensions;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class NodrizasRepo : INodrizasRepo, IGenericRepo<Nodrizas>
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<Nodrizas> _dbSet;

    public NodrizasRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<Nodrizas>();
    }

    public async Task<Nodrizas?> GetByIdAsync(int id, NodrizasQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        return await _dbSet.GetFirstOrDefaultAsync(
            n => n.Id == id && !n.<PERSON>,
            options,
            cancellationToken);
    }

    public async Task<List<Nodrizas>> GetAllAsync(NodrizasQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.GetWhereAsync(n => !n.Borrada, options, cancellationToken);
    }
}