namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class NodrizasRepo : Repository<Nodrizas, APQLitalsaContext>, INodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public NodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<Nodrizas?> GetNodriza(int idNodriza, bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken, n => n.Id == idNodriza);
    }
}