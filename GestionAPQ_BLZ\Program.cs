using GestionAPQ_BLZ;
using GestionAPQ_BLZ.Components;
using GestionAPQ_BLZ.Configuration;
using _Imports = DevExpress.Blazor._Imports;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.CompleteWebUiConfig();
builder.CompleteApplicationConfig();
builder.CompleteInfraestructureConfig();

// Configurar TinyMapper
TinyMapperConfig.Configure();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error", true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(_Imports).Assembly);

app.Run();