﻿namespace GestionAPQ_BLZ.Repositories.Base.DatoLita01;

using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;

public interface IAArticuRepo : IRepository<AArticu>
{
    Task<List<AArticu>> GetBarnicesAsync(string? idBarniz, AArticuQueryOptions options, CancellationToken cancellationToken = default);
    Task<AArticu?> GetBarnizAsync(string idBarniz, AArticuQueryOptions options, CancellationToken cancellationToken = default);
    Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidadesAsync(string? idBarniz, IAViscosRepo aViscosRepo, AArticuQueryOptions options, CancellationToken cancellationToken = default);
}