﻿@page "/"
@inject IMediator Mediator
@inject ICustomToastService ToastService
@inject ILocalStorageService LocalStorageService
@inject ICustomDialogService DialogService
@inject NavigationManager Navigation

<PageTitle>APQ - Inicio</PageTitle>
<CustomLoadingPanel @bind-Visible="Cargando" />
<DxDialogProvider />
@code {

	// ============= VARIABLES Y PROPIEDADES COMUNES =============
	private List<OperarioDTO> _ddOperarios = [];
	private List<AArticuDTO> _ddProductos = [];
	private bool Cargando { get; set; }
	private OperarioDTO? OperarioSelected { get; set; }

	// ============= VARIABLES Y PROPIEDADES MODO NODRIZA =============
	private List<NodrizaDTO> _listaNodrizas = [];
	private NodrizaDTO? NodrizaSelected { get; set; }
	private AArticuDTO? _barnizCargadoNodriza;
	private LoteNodrizaDTO? _ultimoLoteCargadoNodriza;
	private double? _stockBarnizCargadoNodriza;
	// private List<DetalleLoteBarnizDTO> _listaLotesConInspeccionModoNodriza = [];
	// private List<DetalleLoteBarnizDTO> _listaLotesSinInspeccionModoNodriza = [];
	// private List<LotesNodrizasDTO> _listaLotesNodrizaModoNodriza = [];
	// private List<ASalidaDTO> _listaSalidasModoNodriza = [];
	// private object? _selectedDataItemLoteConInspeccionModoNodriza;

	// // ============= VARIABLES Y PROPIEDADES MODO MÁQUINA =============
	// private DxSpinEdit<int?> DxSpinCantidadNecesaria { get; set; }
	// private AArticuDTO? BarnizMaquinaSelected { get; set; }
	// private int? _cantidadNecesariaModoMaquina;
	// private List<DetalleLoteBarnizDTO> _listaLotesConStockConInspeccionModoMaquina = [];
	// private List<DetalleLoteBarnizDTO> _listaLotesConStockSinInspeccionModoMaquina = [];
	// private List<LotesNodrizasDTO> _listaLotesNodrizaModoMaquina = [];
	// private List<ASalidaDTO> _listaSalidasModoMaquina = [];
	// private object? _selectedDataItemLoteConInspeccionModoMaquina;

	// // ============= PROPIEDADES CALCULADAS =============
	// private bool EsModoMaquina { get; set; }
	// private string TxtModo => $"Pasar a {(EsModoMaquina ? "Nodrizas" : "Máquinas")}";

	// private int? IdProductoActual => EsModoMaquina
	// 	? BarnizMaquinaSelected?.Codigo != null ? Convert.ToInt32(BarnizMaquinaSelected.Codigo) : null
	// 	: NodrizaSelected?.Idproducto;

	// private bool TieneOperarioSeleccionado => OperarioSelected != null;
	// private bool TieneProductoSeleccionado => IdProductoActual.HasValue;

	// private object? SelectedDataItemLoteConInspeccion
	// {
	// 	get =>
	// 		EsModoMaquina
	// 			? _selectedDataItemLoteConInspeccionModoMaquina
	// 			: _selectedDataItemLoteConInspeccionModoNodriza;
	// 	set
	// 	{
	// 		if (EsModoMaquina)
	// 			_selectedDataItemLoteConInspeccionModoMaquina = value;
	// 		else
	// 			_selectedDataItemLoteConInspeccionModoNodriza = value;
	// 	}
	// }

	// private List<DetalleLoteBarnizDTO> ListaLotesConStockConInspeccion => EsModoMaquina
	// 	? _listaLotesConStockConInspeccionModoMaquina
	// 	: _listaLotesConInspeccionModoNodriza;

	// private List<DetalleLoteBarnizDTO> ListaLotesConStockSinInspeccion => EsModoMaquina
	// 	? _listaLotesConStockSinInspeccionModoMaquina
	// 	: _listaLotesSinInspeccionModoNodriza;

	// private List<LotesNodrizasDTO> ListaLotesNodriza => EsModoMaquina
	// 	? _listaLotesNodrizaModoMaquina
	// 	: _listaLotesNodrizaModoNodriza;

	// private List<ASalidaDTO> ListaSalidas => EsModoMaquina
	// 	? _listaSalidasModoMaquina
	// 	: _listaSalidasModoNodriza;

	// ============= MÉTODOS PRINCIPALES =============
	protected override async Task OnInitializedAsync()
	{
		await CargarDatosInicio();
		// await EjecutarConCarga(async () =>
		// {
		// 	await CargarDatosInicio();
		// 	await ResetValoresPorDefecto(true);
		// });
	}

	private async Task CargarDatosInicio()
	{
		// ejecutamos querys
		var resultOperarios = await Mediator.Send(GetAllEntities.ForOperarios(new()));
		var resultProdsEnNodrizas = await Mediator.Send(GetAllEntities.ForNodrizas(new()));
		var resultProductos = await Mediator.Send(new GetBarnicesQuery(null, false, new()));

		// comprobamos si hay errores y mostramos dado el caso
		var listaErrores = resultOperarios.Errors.Concat(resultProductos.Errors).Concat(resultProdsEnNodrizas.Errors).ToArray();
		if (listaErrores.Any())
		{
			ToastService.MostrarError(listaErrores.First());
			return;
		}

		// asignamos valor a variables
		_ddOperarios = resultOperarios.Data;
		_ddProductos = resultProductos.Data.Select(i => i.Articulo).OrderBy(i => i.Codigo).ToList();
		_listaNodrizas = resultProdsEnNodrizas.Data.OrderBy(i => i.NumNodriza).ToList();
	}

	// private async Task CargarDatosPostSeleccion()
	// {
	// 	if (!TieneProductoSeleccionado) return;

	// 	if (!EsModoMaquina)
	// 		await CargarDatos_DetallesNodriza();
	// 	await CargarDatos_DetalleLotesInspeccion();
	// 	await CargarDatos_DetalleLotesArticulo();
	// }

	// private async Task CargarDatos_DetallesNodriza()
	// {
	// 	if (NodrizaSelected is not { Id: not null })
	// 		return;

	// 	var numNodriza = NodrizaSelected.Id.Value;

	// 	// ejecutamos query y verificamos errores
	// 	var result = await Mediator.Send(new GetDetalleProductosNodrizaQuery(numNodriza));
	// 	if (result.Errors.Any())
	// 	{
	// 		ToastService.MostrarError(result.Errors.First());
	// 		return;
	// 	}

	// 	// almacenamos en variables la resp
	// 	if (result.Data?.TablaProductoNodriza?.Idproducto == null)
	// 	{
	// 		ToastService.MostrarError($"No se ha encontrado el barniz de la nodriza {numNodriza}.");
	// 		return;
	// 	}

	// 	var detallesTablaProdNodriza = result.Data;
	// 	var tablaProdNodriza = detallesTablaProdNodriza.TablaProductoNodriza;
	// 	var ultimoLoteNodriza = detallesTablaProdNodriza.UltimoLote;

	// 	// verificamos que el producto de la nodriza se encuentra en nuestra lista de productos
	// 	var barniz = _ddProductos.FirstOrDefault(o => o.Codigo.Equals(tablaProdNodriza.Idproducto.ToString()));
	// 	if (barniz == null || string.IsNullOrEmpty(barniz.Codigo))
	// 	{
	// 		ToastService.MostrarError($"No se ha encontrado el  código {tablaProdNodriza.Idproducto} entre el listado de productos.");
	// 		return;
	// 	}

	// 	// actualizamos variables de ui
	// 	_barnizCargadoNodriza = barniz;
	// 	_ultimoLoteCargadoNodriza = ultimoLoteNodriza;
	// 	_stockBarnizCargadoNodriza = _ddProductos.Where(o => o.Codigo.Equals(tablaProdNodriza.Idproducto.ToString())).Sum(o => o.Stock) ?? 0;
	// }

	// private async Task CargarDatos_DetalleLotesInspeccion()
	// {
	// 	var result = await Mediator.Send(new GetDetallesLotesQuery(IdProductoActual.Value.ToString(), false));
	// 	if (result.Errors.Any())
	// 	{
	// 		ToastService.MostrarError(result.Errors.First());
	// 		return;
	// 	}

	// 	// como es una consulta que se usa también en otras vistas, devuelve una lista artículos con sus lotes
	// 	// en este caso, como pasamos elproducto por parámetro, sólo nos devuelve una lista
	// 	// seleccinoamos los lotes así, que debería ser lo mismo que pillar el primer elemento de la listaLotesPorArticulo
	// 	var listaLotesPorArticulo = result.Data.Select(i => i.ListaDetallesLotes).ToList();
	// 	var listaLotesConStock = listaLotesPorArticulo
	// 		.Where(i => i != null && i.Any())
	// 		.SelectMany(i => i)
	// 		.ToList()
	// 		.Where(i => i.Lote.Stock.HasValue && i.Lote.Stock > 0);
	// 	var lotesConStockConInspeccion = listaLotesConStock.Where(i => i.Inspeccion != null);
	// 	var lotesConStockSinInspeccion = listaLotesConStock.Where(i => i.Inspeccion == null);

	// 	if (EsModoMaquina)
	// 	{
	// 		_listaLotesConStockConInspeccionModoMaquina = lotesConStockConInspeccion.ToList();
	// 		_listaLotesConStockSinInspeccionModoMaquina = lotesConStockSinInspeccion.ToList();
	// 	}
	// 	else
	// 	{
	// 		_listaLotesConInspeccionModoNodriza = lotesConStockConInspeccion.ToList();
	// 		_listaLotesSinInspeccionModoNodriza = lotesConStockSinInspeccion.ToList();
	// 	}
	// }

	// private async Task CargarDatos_DetalleLotesArticulo()
	// {
	// 	var result = await Mediator.Send(new GetLotesNodrizasySalidasProductoQuery(IdProductoActual.Value));
	// 	if (result.Errors.Any())
	// 	{
	// 		ToastService.MostrarError(result.Errors.First());
	// 		return;
	// 	}

	// 	if (EsModoMaquina)
	// 	{
	// 		_listaLotesNodrizaModoMaquina = result.Data.ListaLotesNodrizas;
	// 		_listaSalidasModoMaquina = result.Data.ListaSalidas;
	// 	}
	// 	else
	// 	{
	// 		_listaLotesNodrizaModoNodriza = result.Data.ListaLotesNodrizas;
	// 		_listaSalidasModoNodriza = result.Data.ListaSalidas;
	// 	}
	// }

	// private async Task ResetValoresPorDefecto(bool resetVariablesModo)
	// {
	// 	// variables globales de la página
	// 	var ultOperarioSelected = await LocalStorageService.GetItemAsync<OperarioDTO>(EnumKeysLocalStorage.Operario);
	// 	if (ultOperarioSelected is not null && _ddOperarios.Any())
	// 		ultOperarioSelected = _ddOperarios.FirstOrDefault(i => i.Id == ultOperarioSelected.Id && i.Operario == ultOperarioSelected.Operario);
	// 	OperarioSelected = ultOperarioSelected;

	// 	// reset de todas las variables específicas para modo nodriza y modo máquina
	// 	if (resetVariablesModo)
	// 	{
	// 		if (EsModoMaquina)
	// 			ResetVariablesModoMaquina();
	// 		else
	// 			ResetVariablesModoNodriza();
	// 	}
	// 	else
	// 	{
	// 		if (EsModoMaquina)
	// 			await HandleUpdateBarnizMaquina(BarnizMaquinaSelected);
	// 		else
	// 			await HandleClickCambioNodriza(NodrizaSelected);
	// 	}
	// }

	// private void ResetVariablesModoNodriza()
	// {
	// 	NodrizaSelected = null;
	// 	_selectedDataItemLoteConInspeccionModoNodriza = null;
	// 	_barnizCargadoNodriza = null;
	// 	_ultimoLoteCargadoNodriza = null;
	// 	_stockBarnizCargadoNodriza = null;
	// 	_listaLotesConInspeccionModoNodriza = [];
	// 	_listaLotesSinInspeccionModoNodriza = [];
	// 	_listaLotesNodrizaModoNodriza = [];
	// 	_listaSalidasModoNodriza = [];
	// }

	// private void ResetVariablesModoMaquina()
	// {
	// 	BarnizMaquinaSelected = null;
	// 	_selectedDataItemLoteConInspeccionModoMaquina = null;
	// 	_cantidadNecesariaModoMaquina = null;
	// 	_listaLotesConStockConInspeccionModoMaquina = [];
	// 	_listaLotesConStockSinInspeccionModoMaquina = [];
	// 	_listaLotesNodrizaModoMaquina = [];
	// 	_listaSalidasModoMaquina = [];
	// }

	// private void ResetListasLotesInspeccion()
	// {
	// 	if (EsModoMaquina)
	// 	{
	// 		_listaLotesConStockConInspeccionModoMaquina = [];
	// 		_listaLotesConStockSinInspeccionModoMaquina = [];
	// 		_selectedDataItemLoteConInspeccionModoMaquina = null;
	// 	}
	// 	else
	// 	{
	// 		_listaLotesConInspeccionModoNodriza = [];
	// 		_listaLotesSinInspeccionModoNodriza = [];
	// 		_selectedDataItemLoteConInspeccionModoNodriza = null;
	// 	}
	// }

	// private void ResetDatosPostCargaLote()
	// {
	// 	if (EsModoMaquina)
	// 	{
	// 		var barnizSelected = BarnizMaquinaSelected;
	// 		ResetVariablesModoMaquina();
	// 		BarnizMaquinaSelected = barnizSelected;
	// 	}
	// 	else
	// 	{
	// 		var nodrizaSelected = NodrizaSelected;
	// 		ResetVariablesModoNodriza();
	// 		NodrizaSelected = nodrizaSelected;
	// 	}
	// }

	// private async Task HandleClickCambiarModo()
	// {
	// 	EsModoMaquina = !EsModoMaquina;
	// 	await ResetValoresPorDefecto(false);
	// }

	// private async Task HandleUpdateOperarioSeleccionado()
	// {
	// 	if (OperarioSelected is not null)
	// 		await LocalStorageService.SetItemAsync(EnumKeysLocalStorage.Operario, OperarioSelected);
	// 	else
	// 		await LocalStorageService.RemoveItemAsync(EnumKeysLocalStorage.Operario);
	// }

	// private async Task HandleClickCambioNodriza(TablaProductosNodrizasDTO? nuevaNodrizaSelected)
	// {
	// 	await ResetValoresPorDefecto(true);
	// 	NodrizaSelected = nuevaNodrizaSelected;
	// 	await EjecutarConCarga(async () => await CargarDatosPostSeleccion());
	// }

	// private async Task HandleUpdateBarnizMaquina(AArticuDTO? nuevoBarnizMaquinaSelected)
	// {
	// 	await ResetValoresPorDefecto(true);
	// 	BarnizMaquinaSelected = nuevoBarnizMaquinaSelected;

	// 	if (TieneProductoSeleccionado)
	// 		await EjecutarConCarga(async () => await CargarDatosPostSeleccion());
	// }

	// private async Task HandleClickActualizar()
	// {
	// 	if (!TieneProductoSeleccionado)
	// 	{
	// 		ToastService.MostrarError("Tienes que seleccionar " + (EsModoMaquina ? "un producto." : "una nodriza con un producto cargado."));
	// 		return;
	// 	}

	// 	if (EsModoMaquina)
	// 		await HandleUpdateBarnizMaquina(BarnizMaquinaSelected);
	// 	else
	// 		await HandleClickCambioNodriza(NodrizaSelected);
	// }

	// private async Task HandleClickBuscarLote()
	// {
	// 	if (!TieneOperarioSeleccionado)
	// 	{
	// 		ToastService.MostrarError("Tienes que seleccionar un operario.");
	// 		return;
	// 	}

	// 	if (!TieneProductoSeleccionado)
	// 	{
	// 		ToastService.MostrarError("Tienes que seleccionar " + (EsModoMaquina ? "un producto" : "una nodriza con un producto cargado"));
	// 		return;
	// 	}

	// 	if (EsModoMaquina && _cantidadNecesariaModoMaquina is null or <= 0)
	// 	{
	// 		ToastService.MostrarError("Debes indicar una cantidad en positivo para llevar a máquina.");
	// 		if (EsModoMaquina && _cantidadNecesariaModoMaquina == null)
	// 		{
	// 			_cantidadNecesariaModoMaquina = null;
	// 			await InvokeAsync(() => StateHasChanged());
	// 			await DxSpinCantidadNecesaria.FocusAsync();
	// 		}

	// 		return;
	// 	}

	// 	await EjecutarConCarga(async () =>
	// 	{
	// 		if (!EsModoMaquina)
	// 		{
	// 			// ejecutamos query para obtener siguiente lote hipotético a cargar
	// 			var result = await Mediator.Send(new GetSiguienteLoteNodrizaQuery(NodrizaSelected.IdNodriza.Value));
	// 			var detalleLote = result.Data;
	// 			var lote = detalleLote?.Lote;

	// 			if (result.Errors.Any())
	// 			{
	// 				// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
	// 				_selectedDataItemLoteConInspeccionModoNodriza = new object();
	// 				await InvokeAsync(() => StateHasChanged());
	// 				_selectedDataItemLoteConInspeccionModoNodriza = null;
	// 				ToastService.MostrarError(result.Errors.First());
	// 			}
	// 			else if (lote == null)
	// 			{
	// 				// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
	// 				_selectedDataItemLoteConInspeccionModoNodriza = new object();
	// 				await InvokeAsync(() => StateHasChanged());
	// 				_selectedDataItemLoteConInspeccionModoNodriza = null;
	// 				ToastService.MostrarError("No se ha podido encontrar el siguiente lote a cargar.");
	// 			}
	// 			else
	// 			{
	// 				// ya con el lote, marcamos la línea correspondiente en el grid y sacamos lote y ubicación
	// 				// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
	// 				_selectedDataItemLoteConInspeccionModoNodriza = _listaLotesConInspeccionModoNodriza
	// 					.FirstOrDefault(i => i.Lote.Codigo.Equals(lote.Codigo) && i.Lote.Lote.Equals(lote.Lote) && i.Lote.Ubicacion.Equals(lote.Ubicacion)) ?? new object();
	// 				await InvokeAsync(() => StateHasChanged());
	// 				_selectedDataItemLoteConInspeccionModoNodriza ??= null;
	// 				// como está embebida en EjecutarConCarga y el dialogservice no termina hasta que se pulsa un botón, la ejecutamos como una task sin esperarla
	// 				Task.Run(async () => await InvokeAsync(async () => { await DialogService.MostrarInfo("Resultado", $"LOTE: {lote.Lote} / UBICACIÓN: {lote.Ubicacion}", "Ok"); }));
	// 			}
	// 		}
	// 		else
	// 		{
	// 			// lanzamos query para modo máquina
	// 			var result = await Mediator.Send(new GetSiguientesLotesMaquinaQuery(_cantidadNecesariaModoMaquina.Value, Convert.ToInt32(BarnizMaquinaSelected.Codigo)));
	// 			var listaSiguientesDetallesLote = result.Data;

	// 			if (result.Errors.Any())
	// 			{
	// 				// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
	// 				_selectedDataItemLoteConInspeccionModoMaquina = new object();
	// 				await InvokeAsync(() => StateHasChanged());
	// 				_selectedDataItemLoteConInspeccionModoMaquina = null;
	// 				ToastService.MostrarError(result.Errors.First());
	// 			}
	// 			else if (listaSiguientesDetallesLote == null || listaSiguientesDetallesLote.Count == 0)
	// 			{
	// 				// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
	// 				_selectedDataItemLoteConInspeccionModoNodriza = new object();
	// 				await InvokeAsync(() => StateHasChanged());
	// 				_selectedDataItemLoteConInspeccionModoNodriza = null;
	// 				ToastService.MostrarError("No se han podido encontrar los siguientes lotes a cargar.");
	// 			}
	// 			else
	// 			{
	// 				var listaSiguientesLotes = result.Data;
	// 				// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
	// 				_selectedDataItemLoteConInspeccionModoMaquina = new object();
	// 				await InvokeAsync(() => StateHasChanged());
	// 				_selectedDataItemLoteConInspeccionModoMaquina = null;
	// 				var listaLotesString = listaSiguientesLotes.Select(i => $"LOTE: {i.Lote.Lote} / UBICACIÓN: {i.Lote.Ubicacion} / CANTIDAD: {i.Lote.Stock}Kg / STOCK PENDIENTE: {i.CantidadPendienteLote} KG").ToList();
	// 				// como está embebida en EjecutarConCarga y el dialogservice no termina hasta que se pulsa un botón, la ejecutamos como una task sin esperarla
	// 				Task.Run(async () => await InvokeAsync(async () => { await DialogService.MostrarInfo("Resultado", string.Join("\r\n", listaLotesString), "Ok", "700"); }));
	// 			}
	// 		}
	// 	});
	// }

	// private async Task HandleClickCargarLote()
	// {
	// 	// comprobaciones básicas
	// 	if (!TieneOperarioSeleccionado)
	// 	{
	// 		ToastService.MostrarError("Tienes que seleccionar un operario.");
	// 		return;
	// 	}

	// 	if (!TieneProductoSeleccionado)
	// 	{
	// 		ToastService.MostrarError("Tienes que seleccionar " + (EsModoMaquina ? "un producto" : "una nodriza con un producto cargado"));
	// 		return;
	// 	}

	// 	var loteSeleccionado = ((DetalleLoteBarnizDTO?)SelectedDataItemLoteConInspeccion)?.Lote;
	// 	if (loteSeleccionado == null)
	// 	{
	// 		ToastService.MostrarError("Debes seleccionar un lote previamente.");
	// 		return;
	// 	}

	// 	// comprobaciones sobre la fecha de caducidad del barniz
	// 	if (loteSeleccionado.Fechalote <= DateTime.Now)
	// 	{
	// 		var grabar = await DialogService.MostrarConfirmacionEstiloWarning(
	// 			"Cargar Lote - Chequeo de fecha",
	// 			$"El barniz {loteSeleccionado.Codigo}, lote {loteSeleccionado.Lote} está caducado.\r\nConsulta con Calidad.", "Grabar", "Cancelar");
	// 		if (!grabar)
	// 			return;
	// 	}

	// 	// si hemos llegado aquí, podemos cargar el lote
	// 	var numNodriza = EsModoMaquina ? 0 : NodrizaSelected.IdNodriza.Value;
	// 	await EjecutarConCarga(async () =>
	// 	{
	// 		var loteBarniz = new LotesNodrizasDTO
	// 		{
	// 			Fecha = DateTime.Now,
	// 			Hora = DateTime.Now.TimeOfDay,
	// 			Nodriza = numNodriza,
	// 			Idproducto = int.Parse(loteSeleccionado.Codigo),
	// 			Lote = loteSeleccionado.Lote,
	// 			Operario = OperarioSelected?.Operario ?? "N/A",
	// 			Ubicacion = loteSeleccionado.Ubicacion
	// 		};
	// 		var result = await Mediator.Send(new GrabarLotesNodrizasCommand(loteBarniz));
	// 		if (result.Errors.Any())
	// 		{
	// 			ToastService.MostrarError($"{result.Errors.First()}");
	// 		}
	// 		else
	// 		{
	// 			ResetDatosPostCargaLote();
	// 			ToastService.MostrarOk($"Lote {loteBarniz.Lote} registrado. Recuerda dar consumo en el ERP a este material");
	// 			await CargarDatosPostSeleccion();
	// 		}
	// 	});
	// }

	// private void HandleClickEstadoInspecciones()
	// {
	// 	if (!TieneProductoSeleccionado)
	// 	{
	// 		ToastService.MostrarError("Tienes que seleccionar " + (EsModoMaquina ? "un producto." : "una nodriza con un producto cargado."));
	// 		return;
	// 	}

	// 	Navigation.NavigateTo($"EstadoInspecciones/{IdProductoActual}");
	// }

	// private void GridLotesConInspeccion_CustomizeEditModel(GridCustomizeEditModelEventArgs e)
	// {
	// 	if (!TieneOperarioSeleccionado)
	// 	{
	// 		ToastService.MostrarError("Tienes que seleccionar un operario.");
	// 		return;
	// 	}

	// 	var detalleALoteOriginalDto = (DetalleLoteBarnizDTO)e.DataItem;
	// 	var detalleALoteEditDto = (DetalleLoteBarnizDTO)e.EditModel;
	// 	detalleALoteOriginalDto.Incidencia ??= new IncidenciaDTO(); // inicializamos la incidencia para que nos deje editarla, si esta fuera null.
	// 	if (detalleALoteOriginalDto.Incidencia.Id != null)
	// 	{
	// 		detalleALoteEditDto.Incidencia = new IncidenciaDTO
	// 		{
	// 			Id = detalleALoteOriginalDto.Incidencia.Id,
	// 			Incidencia = detalleALoteOriginalDto.Incidencia.Incidencia
	// 		};
	// 	}
	// 	else
	// 	{
	// 		detalleALoteEditDto.Incidencia = new IncidenciaDTO
	// 		{
	// 			Id = detalleALoteOriginalDto.Incidencia.Id,
	// 			Incidencia = detalleALoteOriginalDto.Incidencia.Incidencia,
	// 			CreadoPor = detalleALoteOriginalDto.Incidencia.CreadoPor,
	// 			Codigo = detalleALoteOriginalDto.Lote.Codigo,
	// 			Lote = detalleALoteOriginalDto.Lote.Lote,
	// 			Ubicacion = detalleALoteOriginalDto.Lote.Ubicacion
	// 		};
	// 	}
	// }

	// private async Task GridLotesConInspeccion_EditModelSaving(GridEditModelSavingEventArgs e)
	// {
	// 	var detalleALoteOriginalDto = (DetalleLoteBarnizDTO)e.DataItem;
	// 	var detalleALoteEditDto = (DetalleLoteBarnizDTO)e.EditModel;
	// 	var incidenciaOriginalDto = detalleALoteOriginalDto.Incidencia;
	// 	var incidenciaEditDto = detalleALoteEditDto.Incidencia;
	// 	var incidenciaOriginalStr = incidenciaOriginalDto?.Incidencia?.Trim() ?? string.Empty;
	// 	var incidenciaEditStr = incidenciaEditDto?.Incidencia?.Trim() ?? string.Empty;

	// 	// comprobamos si hay cambios en la incidencia
	// 	if (string.IsNullOrEmpty(incidenciaOriginalStr) && string.IsNullOrEmpty(incidenciaEditStr))
	// 	{
	// 		ToastService.MostrarWarning("Para registrar una incidencia, no puedes dejarla vacía.");
	// 		return;
	// 	}

	// 	if (incidenciaOriginalStr.Equals(incidenciaEditStr))
	// 	{
	// 		ToastService.MostrarWarning("No ha habido cambios en la incidencia, por tanto se mantiene igual.");
	// 		return;
	// 	}

	// 	// comprobamos si hay que eliminar de bbdd
	// 	if (string.IsNullOrEmpty(incidenciaEditStr))
	// 	{
	// 		await GridLotesConInspeccion_HandleDeleteEvt(detalleALoteOriginalDto);
	// 		return;
	// 	}

	// 	// añadimos / actualizamos la incidencia
	// 	await EjecutarConCarga(async () =>
	// 	{
	// 		if (!incidenciaEditDto.Id.HasValue)
	// 			incidenciaEditDto.CreadoPor = OperarioSelected?.Operario;
	// 		var result = await Mediator.Send(new GrabarIncidenciaCommand(incidenciaEditDto));
	// 		if (result.Errors.Any())
	// 		{
	// 			ToastService.MostrarError(result.Errors.First());
	// 			e.Cancel = true;
	// 			return;
	// 		}

	// 		ResetListasLotesInspeccion();
	// 		await CargarDatos_DetalleLotesInspeccion();
	// 		ToastService.MostrarOk($"Incidencia {(string.IsNullOrEmpty(incidenciaOriginalStr) ? "añadida" : "editada")} con éxito.");
	// 	});
	// }

	// private async Task GridLotesConInspeccion_HandleDeleteEvt(DetalleLoteBarnizDTO detalleALote)
	// {
	// 	var incidenciaDto = detalleALote.Incidencia;
	// 	var idIncidencia = incidenciaDto?.Id;

	// 	// comprobaciones básicas
	// 	if (idIncidencia == null)
	// 	{
	// 		ToastService.MostrarWarning("No puedes eliminar una incidencia que todavía no ha sido registrada en el sistema.");
	// 		return;
	// 	}

	// 	var resp = await DialogService.MostrarConfirmacionEstiloDanger("Atención",
	// 		@"¿Seguro que deseas eliminar esta incidencia?",
	// 		"Sí",
	// 		"No");
	// 	if (!resp)
	// 		return;

	// 	// empezamos mediatr
	// 	await EjecutarConCarga(async () =>
	// 	{
	// 		var result = await Mediator.Send(new DeleteIncidenciaCommand(idIncidencia.Value));
	// 		if (result.Errors.Any())
	// 			ToastService.MostrarError(result.Errors.First());
	// 		else
	// 		{
	// 			ResetListasLotesInspeccion();
	// 			await CargarDatos_DetalleLotesInspeccion();
	// 			ToastService.MostrarOk("Incidencia eliminada con éxito.");
	// 		}
	// 	});
	// }

	// private async Task EjecutarConCarga(Func<Task> accion)
	// {
	// 	Cargando = true;
	// 	await InvokeAsync(() => StateHasChanged());
	// 	await accion();
	// 	Cargando = false;
	// 	await InvokeAsync(() => StateHasChanged());
	// }
}