﻿namespace GestionAPQ_BLZ.Repositories.Base;

using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

public abstract class Repository<TEntity, TDbContext> : IRepository<TEntity>
    where TEntity : class
    where TDbContext : DbContext
{
    public Repository(TDbContext dbContext)
    {
        DbContext = dbContext;
        DbSet = DbContext.Set<TEntity>();
    }

    protected TDbContext DbContext { get; }
    protected DbSet<TEntity> DbSet { get; }

    public async Task<int> UpdateEntityWithId(CancellationToken cancellationToken, TEntity entity)
    {
        DbSet.Update(entity);
        return await DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<int> DeleteEntity(CancellationToken cancellationToken, TEntity entity)
    {
        DbSet.Remove(entity);
        return await DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<TEntity>> GetAll(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null)
    {
        var query = GetIQueryable(asNoTracking, null, orderBy, orderByDesc, groupBy);
        return await query.ToListAsync(cancellationToken);
    }

    public async Task<List<TEntity>> GetList(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null)
    {
        var query = GetIQueryable(asNoTracking, where, orderBy, orderByDesc, groupBy);
        return await query.ToListAsync(cancellationToken);
    }

    public async Task<TEntity?> GetFirstOrDefault(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null)
    {
        var query = GetIQueryable(asNoTracking, where, orderBy, orderByDesc, groupBy);
        return await query.FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<TEntity?> GetLastOrDefault(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null)
    {
        var query = GetIQueryable(asNoTracking, where, orderBy, orderByDesc, groupBy);
        return await query.LastOrDefaultAsync(cancellationToken);
    }

    public async Task<TEntity?> GetFirstOrDefaultWithIncludesAsync<TProperty>(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, TProperty>> include,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        query = query.Include(include).Where(where);

        if (orderByDesc != null)
            query = query.OrderByDescending(orderByDesc);

        return await query.FirstOrDefaultAsync(cancellationToken);
    }

    private IQueryable<TEntity> GetIQueryable(
        bool asNoTracking,
        Expression<Func<TEntity, bool>>? where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (where != null)
            query = query.Where(where);

        if (orderBy != null)
            query = query.OrderBy(orderBy);
        else if (orderByDesc != null)
            query = query.OrderByDescending(orderByDesc);

        if (groupBy != null)
            query = query
                .GroupBy(groupBy)
                .Select(g => g.FirstOrDefault());

        return query;
    }
}