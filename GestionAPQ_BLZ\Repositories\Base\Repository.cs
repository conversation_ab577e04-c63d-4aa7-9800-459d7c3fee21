﻿namespace GestionAPQ_BLZ.Repositories.Base;

using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

public abstract class Repository<TEntity, TDbContext> : IRepository<TEntity>
    where TEntity : class
    where TDbContext : DbContext
{
    public Repository(TDbContext dbContext)
    {
        DbContext = dbContext;
        DbSet = DbContext.Set<TEntity>();
    }

    protected TDbContext DbContext { get; }
    protected DbSet<TEntity> DbSet { get; }


    public void AddEntity(TEntity entity)
    {
        DbSet.Add(entity);
    }

    public void AddRangeEntities(IEnumerable<TEntity> entities)
    {
        DbSet.AddRange(entities);
    }

    public void UpdateEntity(TEntity entity)
    {
        DbSet.Update(entity);
    }

    public void DeleteEntity(TEntity entity)
    {
        DbSet.Remove(entity);
    }

    public void DeleteRangeEntities(IEnumerable<TEntity> entities)
    {
        DbSet.RemoveRange(entities);
    }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<TEntity>> GetAllAsync(
        bool asNoTracking = true,
        CancellationToken cancellationToken = default,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        var query = GetIQueryable(asNoTracking, null, orderBy, orderByDesc);
        return await query.ToListAsync(cancellationToken);
    }

    private IQueryable<TEntity> GetIQueryable(
        bool asNoTracking,
        Expression<Func<TEntity, bool>>? where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (where != null)
            query = query.Where(where);

        if (orderBy != null)
            query = query.OrderBy(orderBy);
        else if (orderByDesc != null)
            query = query.OrderByDescending(orderByDesc);

        return query;
    }
}