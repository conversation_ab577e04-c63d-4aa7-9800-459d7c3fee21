namespace GestionAPQ_BLZ.Repositories.Base;

using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

public abstract class Repository<TEntity, TDbContext> : IRepository<TEntity>
    where TEntity : class
    where TDbContext : DbContext
{
    public Repository(TDbContext dbContext)
    {
        DbContext = dbContext;
        DbSet = DbContext.Set<TEntity>();
    }

    protected TDbContext DbContext { get; }
    protected DbSet<TEntity> DbSet { get; }


    public void AddEntity(TEntity entity)
    {
        DbSet.Add(entity);
    }

    public void AddRangeEntities(IEnumerable<TEntity> entities)
    {
        DbSet.AddRange(entities);
    }

    public void UpdateEntity(TEntity entity)
    {
        DbSet.Update(entity);
    }

    public void DeleteEntity(TEntity entity)
    {
        DbSet.Remove(entity);
    }

    public void DeleteRangeEntities(IEnumerable<TEntity> entities)
    {
        DbSet.RemoveRange(entities);
    }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<TEntity>> GetAllAsync(
        bool asNoTracking = true,
        CancellationToken cancellationToken = default,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        var query = GetIQueryable(asNoTracking, null, orderBy, orderByDesc);
        return await query.ToListAsync(cancellationToken);
    }

    public async Task<List<TEntity>> GetListAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        var query = GetIQueryable(asNoTracking, where, orderBy, orderByDesc);
        return await query.ToListAsync(cancellationToken);
    }

    public async Task<TEntity?> GetFirstOrDefaultAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        var query = GetIQueryable(asNoTracking, where, orderBy, orderByDesc);
        return await query.FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<TEntity?> GetLastOrDefaultAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        var query = GetIQueryable(asNoTracking, where, orderBy, orderByDesc);
        return await query.LastOrDefaultAsync(cancellationToken);
    }

    public async Task<TEntity?> GetFirstOrDefaultWithIncludesAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        params Expression<Func<TEntity, object>>[] includes)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        foreach (var include in includes)
            query = query.Include(include);

        query = query.Where(where);

        if (orderByDesc != null)
            query = query.OrderByDescending(orderByDesc);

        return await query.FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<TEntity>> GetListWithIncludesAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        params Expression<Func<TEntity, object>>[] includes)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        foreach (var include in includes)
            query = query.Include(include);

        query = query.Where(where);

        if (orderBy != null)
            query = query.OrderBy(orderBy);
        else if (orderByDesc != null)
            query = query.OrderByDescending(orderByDesc);

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<int> CountAsync(
        Expression<Func<TEntity, bool>>? where = null,
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        if (where != null)
            query = query.Where(where);

        return await query.CountAsync(cancellationToken);
    }

    public async Task<bool> AnyAsync(
        Expression<Func<TEntity, bool>>? where = null,
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        if (where != null)
            query = query.Where(where);

        return await query.AnyAsync(cancellationToken);
    }

    public async Task<List<TEntity>> GetPagedAsync(
        int page,
        int pageSize,
        bool asNoTracking = true,
        Expression<Func<TEntity, bool>>? where = null,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        CancellationToken cancellationToken = default)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (where != null)
            query = query.Where(where);

        if (orderBy != null)
            query = query.OrderBy(orderBy);
        else if (orderByDesc != null)
            query = query.OrderByDescending(orderByDesc);

        return await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    private IQueryable<TEntity> GetIQueryable(
        bool asNoTracking,
        Expression<Func<TEntity, bool>>? where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (where != null)
            query = query.Where(where);

        if (orderBy != null)
            query = query.OrderBy(orderBy);
        else if (orderByDesc != null)
            query = query.OrderByDescending(orderByDesc);

        return query;
    }

    protected async Task<TEntity?> GetFirstOrDefault(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        return await GetFirstOrDefaultAsync(asNoTracking, cancellationToken, where, orderBy, orderByDesc);
    }

    protected async Task<List<TEntity>> GetList(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null)
    {
        return await GetListAsync(asNoTracking, cancellationToken, where, orderBy, orderByDesc);
    }
}