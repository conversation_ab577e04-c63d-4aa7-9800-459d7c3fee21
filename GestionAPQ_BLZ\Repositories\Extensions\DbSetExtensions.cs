namespace GestionAPQ_BLZ.Repositories.Extensions;

using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

/// <summary>
/// Extension methods genéricos para DbSet que permiten consultas comunes
/// </summary>
public static class DbSetExtensions
{
    /// <summary>
    /// Obtiene todos los elementos de la tabla aplicando QueryOptions
    /// </summary>
    public static async Task<List<T>> GetAllAsync<T>(
        this DbSet<T> dbSet, 
        QueryOptions options, 
        CancellationToken cancellationToken = default) 
        where T : class
    {
        var query = options.Set(dbSet);
        return await query.ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtiene elementos filtrados aplicando QueryOptions
    /// </summary>
    public static async Task<List<T>> GetWhereAsync<T>(
        this DbSet<T> dbSet, 
        Expression<Func<T, bool>> where,
        QueryOptions options, 
        CancellationToken cancellationToken = default) 
        where T : class
    {
        var query = options.Set(dbSet);
        return await query.Where(where).ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtiene un elemento por condición aplicando QueryOptions
    /// </summary>
    public static async Task<T?> GetFirstOrDefaultAsync<T>(
        this DbSet<T> dbSet, 
        Expression<Func<T, bool>> where,
        QueryOptions options, 
        CancellationToken cancellationToken = default) 
        where T : class
    {
        var query = options.Set(dbSet);
        return await query.Where(where).FirstOrDefaultAsync(cancellationToken);
    }

    /// <summary>
    /// Cuenta elementos aplicando QueryOptions
    /// </summary>
    public static async Task<int> CountAsync<T>(
        this DbSet<T> dbSet, 
        Expression<Func<T, bool>>? where,
        QueryOptions options, 
        CancellationToken cancellationToken = default) 
        where T : class
    {
        var query = options.Set(dbSet);
        
        if (where != null)
            query = query.Where(where);
            
        return await query.CountAsync(cancellationToken);
    }

    /// <summary>
    /// Verifica si existen elementos aplicando QueryOptions
    /// </summary>
    public static async Task<bool> AnyAsync<T>(
        this DbSet<T> dbSet, 
        Expression<Func<T, bool>>? where,
        QueryOptions options, 
        CancellationToken cancellationToken = default) 
        where T : class
    {
        var query = options.Set(dbSet);
        
        if (where != null)
            query = query.Where(where);
            
        return await query.AnyAsync(cancellationToken);
    }
}
