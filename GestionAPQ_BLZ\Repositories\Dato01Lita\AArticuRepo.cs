namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AArticuRepo : Repository<AArticu, Dato01LitaContext>, IAArticuRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public AArticuRepo(Dato01LitaContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<AArticu>> GetBarnicesAsync(string? idBarniz, QueryOptions options, CancellationToken cancellationToken = default)
    {
        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        // Lógica de negocio específica para barnices
        query = query.Where(i => i.Estadis.StartsWith("BABA"));

        if (!string.IsNullOrEmpty(idBarniz))
            query = query.Where(i => i.Codigo.Equals(idBarniz));

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<AArticu?> GetBarnizAsync(string idBarniz, QueryOptions options, CancellationToken cancellationToken = default)
    {
        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        return await query
            .Where(i => i.Estadis.StartsWith("BABA") && i.Codigo.Equals(idBarniz))
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidadesAsync(string? idBarniz, IAViscosRepo aViscosRepo, QueryOptions options, CancellationToken cancellationToken = default)
    {
        // Obtener barnices
        var queryBarnices = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();
        queryBarnices = queryBarnices.Where(i => i.Estadis.StartsWith("BABA"));

        if (!string.IsNullOrEmpty(idBarniz))
            queryBarnices = queryBarnices.Where(i => i.Codigo.Equals(idBarniz));

        // Obtener viscosidades usando el nuevo patrón
        var viscosidades = await aViscosRepo.GetViscosidadesAsync(idBarniz,
            options.AsNoTracking ? new QueryOptions() : new QueryOptions { AsNoTracking = false },
            cancellationToken);

        // Hacer el join en memoria (más simple y mantenible)
        var barnices = await queryBarnices.ToListAsync(cancellationToken);

        var result = from barniz in barnices
            join viscosidad in viscosidades on barniz.Codigo equals viscosidad.Codigo into viscosidadGroup
            from viscosidad in viscosidadGroup.DefaultIfEmpty()
            select (barniz, viscosidad);

        return result.ToList();
    }
}