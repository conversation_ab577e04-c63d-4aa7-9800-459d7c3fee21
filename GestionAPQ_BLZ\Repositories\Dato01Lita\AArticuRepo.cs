﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AArticuRepo : Repository<AArticu, Dato01LitaContext>, IAArticuRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public AArticuRepo(Dato01LitaContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<AArticu>> GetBarnicesAsync(string? idBarniz, QueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.Set(DbSet);

        // Lógica de negocio específica para barnices
        query = query.Where(i => i.Estadis.StartsWith("BABA"));

        if (!string.IsNullOrEmpty(idBarniz))
            query = query.Where(i => i.Codigo.Equals(idBarniz));

        return await query.ToListAsync(cancellationToken);
    }


    public async Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidadesAsync(string? idBarniz,
        IAViscosRepo aViscosRepo, QueryOptions options, CancellationToken cancellationToken = default)
    {
        var queryBarnices = options.Set(DbSet);
        queryBarnices = queryBarnices.Where(i => i.Estadis.StartsWith("BABA"));

        if (!string.IsNullOrEmpty(idBarniz))
            queryBarnices = queryBarnices.Where(i => i.Codigo.Equals(idBarniz));

        var queryViscosidades = aViscosRepo.GetViscosidadesQuery(idBarniz, options);

        // Join en base de datos usando ambos repositorios
        var query = from barniz in queryBarnices
            join viscosidad in queryViscosidades on barniz.Codigo equals viscosidad.Codigo into viscosidadGroup
            from viscosidad in viscosidadGroup.DefaultIfEmpty()
            select new { barniz, viscosidad };

        var result = await query.ToListAsync(cancellationToken);
        return result.Select(x => (x.barniz, x.viscosidad)).ToList();
    }
}