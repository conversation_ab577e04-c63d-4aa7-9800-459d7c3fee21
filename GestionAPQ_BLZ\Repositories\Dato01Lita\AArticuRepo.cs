namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AArticuRepo : Repository<AArticu, Dato01LitaContext>, IAArticuRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public AArticuRepo(Dato01LitaContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<AArticu>> GetBarnicesAsync(string? idBarniz, QueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.Set(DbSet);

        // Lógica de negocio específica para barnices
        query = query.Where(i => i.Estadis.StartsWith("BABA"));

        if (!string.IsNullOrEmpty(idBarniz))
            query = query.Where(i => i.Codigo.Equals(idBarniz));

        return await query.ToListAsync(cancellationToken);
    }


    public async Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidadesAsync(string? idBarniz,
        IAViscosRepo aViscosRepo, QueryOptions options, CancellationToken cancellationToken = default)
    {
        // ✅ Join completo en base de datos - misma BD, una sola consulta
        var query = options.Set(DbSet);

        // Join directo con la tabla AViscos en la misma base de datos
        var joinQuery = from barniz in query
            join viscosidad in DbContext.Set<AViscos>() on barniz.Codigo equals viscosidad.Codigo into viscosidadGroup
            from viscosidad in viscosidadGroup.DefaultIfEmpty()
            where barniz.Estadis.StartsWith("BABA") &&
                  (string.IsNullOrEmpty(idBarniz) || barniz.Codigo.Equals(idBarniz))
            select new { barniz, viscosidad };

        var result = await joinQuery.ToListAsync(cancellationToken);
        return result.Select(x => (x.barniz, x.viscosidad)).ToList();
    }
}