namespace GestionAPQ_BLZ.Repositories;

/// <summary>
/// Factory para crear QueryOptions con configuraciones predefinidas comunes
/// </summary>
public static class QueryOptionsFactory
{
    #region NodrizasQueryOptions Factory Methods
    
    /// <summary>
    /// Crea QueryOptions para consultas básicas de nodrizas
    /// </summary>
    public static NodrizasQueryOptions ForNodrizas(bool includeLotes = false, bool asNoTracking = true)
        => new() { IncludeLotes = includeLotes, AsNoTracking = asNoTracking };
    
    /// <summary>
    /// Crea QueryOptions para consultas de nodrizas con todos los lotes incluidos
    /// </summary>
    public static NodrizasQueryOptions ForNodrizasWithLotes(bool asNoTracking = true)
        => new() { IncludeLotes = true, AsNoTracking = asNoTracking };
    
    /// <summary>
    /// Crea QueryOptions para consultas de nodrizas sin lotes (solo datos básicos)
    /// </summary>
    public static NodrizasQueryOptions ForNodrizasBasic(bool asNoTracking = true)
        => new() { IncludeLotes = false, AsNoTracking = asNoTracking };
    
    #endregion

    #region OperariosQueryOptions Factory Methods
    
    /// <summary>
    /// Crea QueryOptions para consultas básicas de operarios
    /// </summary>
    public static OperariosQueryOptions ForOperarios(bool includeLotes = false, bool asNoTracking = true)
        => new() { IncludeLotes = includeLotes, AsNoTracking = asNoTracking };
    
    /// <summary>
    /// Crea QueryOptions para consultas de operarios con todos los lotes incluidos
    /// </summary>
    public static OperariosQueryOptions ForOperariosWithLotes(bool asNoTracking = true)
        => new() { IncludeLotes = true, AsNoTracking = asNoTracking };
    
    /// <summary>
    /// Crea QueryOptions para consultas de operarios sin lotes (solo datos básicos)
    /// </summary>
    public static OperariosQueryOptions ForOperariosBasic(bool asNoTracking = true)
        => new() { IncludeLotes = false, AsNoTracking = asNoTracking };
    
    #endregion

    #region LotesNodrizasQueryOptions Factory Methods
    
    /// <summary>
    /// Crea QueryOptions para consultas básicas de lotes
    /// </summary>
    public static LotesNodrizasQueryOptions ForLotes(
        bool includeNodriza = false, 
        bool includeOperario = true, 
        bool asNoTracking = true)
        => new() 
        { 
            IncludeNodriza = includeNodriza, 
            IncludeOperario = includeOperario, 
            AsNoTracking = asNoTracking 
        };
    
    /// <summary>
    /// Crea QueryOptions para consultas de lotes con toda la información relacionada
    /// </summary>
    public static LotesNodrizasQueryOptions ForLotesComplete(bool asNoTracking = true)
        => new() 
        { 
            IncludeNodriza = true, 
            IncludeOperario = true, 
            AsNoTracking = asNoTracking 
        };
    
    /// <summary>
    /// Crea QueryOptions para consultas de lotes solo con información del operario
    /// </summary>
    public static LotesNodrizasQueryOptions ForLotesWithOperario(bool asNoTracking = true)
        => new() 
        { 
            IncludeNodriza = false, 
            IncludeOperario = true, 
            AsNoTracking = asNoTracking 
        };
    
    /// <summary>
    /// Crea QueryOptions para consultas de lotes solo con información de la nodriza
    /// </summary>
    public static LotesNodrizasQueryOptions ForLotesWithNodriza(bool asNoTracking = true)
        => new() 
        { 
            IncludeNodriza = true, 
            IncludeOperario = false, 
            AsNoTracking = asNoTracking 
        };
    
    /// <summary>
    /// Crea QueryOptions para consultas de lotes sin información relacionada (solo datos básicos)
    /// </summary>
    public static LotesNodrizasQueryOptions ForLotesBasic(bool asNoTracking = true)
        => new() 
        { 
            IncludeNodriza = false, 
            IncludeOperario = false, 
            AsNoTracking = asNoTracking 
        };
    
    #endregion

    #region AArticuQueryOptions Factory Methods
    
    /// <summary>
    /// Crea QueryOptions para consultas de artículos/barnices
    /// </summary>
    public static AArticuQueryOptions ForArticulos(bool asNoTracking = true)
        => new() { AsNoTracking = asNoTracking };
    
    /// <summary>
    /// Crea QueryOptions para consultas de barnices (alias para ForArticulos)
    /// </summary>
    public static AArticuQueryOptions ForBarnices(bool asNoTracking = true)
        => ForArticulos(asNoTracking);
    
    #endregion

    #region AViscosQueryOptions Factory Methods
    
    /// <summary>
    /// Crea QueryOptions para consultas de viscosidades
    /// </summary>
    public static AViscosQueryOptions ForViscosidades(bool asNoTracking = true)
        => new() { AsNoTracking = asNoTracking };
    
    #endregion

    #region Métodos de Conveniencia Comunes
    
    /// <summary>
    /// Crea QueryOptions con AsNoTracking = true (para consultas de solo lectura)
    /// </summary>
    public static T ReadOnly<T>() where T : QueryOptions, new()
        => new() { AsNoTracking = true };
    
    /// <summary>
    /// Crea QueryOptions con AsNoTracking = false (para modificaciones)
    /// </summary>
    public static T ForUpdate<T>() where T : QueryOptions, new()
        => new() { AsNoTracking = false };
    
    #endregion
}
