namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;

public record GetAllEntitiesQuery<TEntity, TDto>(QueryOptions QueryOptions)
    : IRequest<ListResult<TDto>> where TEntity : class;

public class GetAllEntitiesQueryHandler<TEntity, TDto> : IRequestHandler<GetAllEntitiesQuery<TEntity, TDto>, ListResult<TDto>>
    where TEntity : class
{
    private readonly IGenericRepo<TEntity> _repository;

    public GetAllEntitiesQueryHandler(IGenericRepo<TEntity> repository)
    {
        _repository = repository;
    }

    public async Task<ListResult<TDto>> Handle(GetAllEntitiesQuery<TEntity, TDto> request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TDto>
        {
            Data = [],
            Errors = []
        };

        try
        {
            // Usar las QueryOptions pasadas o crear unas por defecto
            var entities = await _repository.GetAllAsync(request.QueryOptions, cancellationToken);

            result.Data = TinyMapper.Map<List<TDto>>(entities);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}
