namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base;

public record GetAllEntitiesQuery<TEntity, TDto>(bool AsNoTracking = true)
    : IRequest<ListResult<TDto>> where TEntity : class;

public class
    GetAllEntitiesQueryHandler<TEntity, TDto> : IRequestHandler<GetAllEntitiesQuery<TEntity, TDto>, ListResult<TDto>>
    where TEntity : class
{
    private readonly IRepository<TEntity> _repository;

    public GetAllEntitiesQueryHandler(IRepository<TEntity> repository)
    {
        _repository = repository;
    }

    public async Task<ListResult<TDto>> Handle(GetAllEntitiesQuery<TEntity, TDto> request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<TDto>
        {
            Data = [],
            Errors = []
        };

        try
        {
            var entities = await _repository.GetAllAsync(request.AsNoTracking, cancellationToken);
            result.Data = TinyMapper.Map<List<TDto>>(entities);
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}