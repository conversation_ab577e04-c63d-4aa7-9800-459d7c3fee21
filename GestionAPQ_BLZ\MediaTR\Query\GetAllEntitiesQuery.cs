namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;

public record GetAllEntitiesQuery<TEntity, TDto>(QueryOptions QueryOptions)
    : IRequest<ListResult<TDto>> where TEntity : class;

/// <summary>
/// Factory methods para crear queries GetAllEntities con tipos inferidos
/// </summary>
public static class GetAllEntities
{
    public static GetAllEntitiesQuery<Nodrizas, NodrizaDTO> ForNodrizas(NodrizasQueryOptions options)
        => new(options);

    public static GetAllEntitiesQuery<Operarios, OperarioDTO> ForOperarios(OperariosQueryOptions options)
        => new(options);
}

public class GetAllEntitiesQueryHandler<TEntity, TDto> : IRequestHandler<GetAllEntitiesQuery<TEntity, TDto>, ListResult<TDto>>
    where TEntity : class
{
    private readonly IServiceProvider _serviceProvider;

    public GetAllEntitiesQueryHandler(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task<ListResult<TDto>> Handle(GetAllEntitiesQuery<TEntity, TDto> request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TDto>
        {
            Data = [],
            Errors = []
        };

        try
        {
            // Usar las QueryOptions pasadas o crear unas por defecto
            var options = request.QueryOptions;

            // Switch según el tipo de entidad
            var entities = typeof(TEntity).Name switch
            {
                nameof(Nodrizas) => await GetNodrizas(options, cancellationToken),
                nameof(Operarios) => await GetOperarios(options, cancellationToken),
                _ => throw new NotSupportedException($"Entity type {typeof(TEntity).Name} not supported for GetAll operation")
            };

            result.Data = TinyMapper.Map<List<TDto>>(entities);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private async Task<List<object>> GetNodrizas(QueryOptions options, CancellationToken cancellationToken)
    {
        var repo = _serviceProvider.GetRequiredService<INodrizasRepo>();
        var nodrizasOptions = options as NodrizasQueryOptions ?? new NodrizasQueryOptions 
        { 
            AsNoTracking = options.AsNoTracking 
        };
        
        var entities = await repo.GetAllAsync(nodrizasOptions, cancellationToken);
        return entities.Cast<object>().ToList();
    }

    private async Task<List<object>> GetOperarios(QueryOptions options, CancellationToken cancellationToken)
    {
        var repo = _serviceProvider.GetRequiredService<IOperariosRepo>();
        var operariosOptions = options as OperariosQueryOptions ?? new OperariosQueryOptions 
        { 
            AsNoTracking = options.AsNoTracking 
        };
        
        var entities = await repo.GetAllAsync(operariosOptions, cancellationToken);
        return entities.Cast<object>().ToList();
    }
}
