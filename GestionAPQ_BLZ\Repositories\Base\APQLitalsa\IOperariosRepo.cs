namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface IOperariosRepo
{
    // Métodos genéricos
    Task<List<Operarios>> GetAllAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default);
    Task<List<Operarios>> GetActivosAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default);
    Task<Operarios?> GetByIdAsync(int id, OperariosQueryOptions options, CancellationToken cancellationToken = default);
    Task<int> CountAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default);
    Task<int> CountActivosAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default);
}