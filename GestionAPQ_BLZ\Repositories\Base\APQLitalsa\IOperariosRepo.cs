﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Options;

public interface IOperariosRepo : IRepository<Operarios>
{
    Task<Operarios?> GetByIdAsync(int id, OperarioQueryOptions? options = null, CancellationToken cancellationToken = default);
    Task<List<Operarios>> GetActivosAsync(OperarioQueryOptions? options = null, CancellationToken cancellationToken = default);

    // Métodos de conveniencia para casos comunes
    Task<Operarios?> GetByIdWithLotesAsync(int id, CancellationToken cancellationToken = default);
    Task<List<Operarios>> GetActivosWithLotesAsync(CancellationToken cancellationToken = default);
}