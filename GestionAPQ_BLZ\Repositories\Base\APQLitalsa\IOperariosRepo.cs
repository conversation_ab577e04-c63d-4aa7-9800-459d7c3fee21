﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface IOperariosRepo : IRepository<Operarios>
{
    Task<Operarios?> GetByIdAsync(int id, OperariosQueryOptions options, CancellationToken cancellationToken = default);
    Task<List<Operarios>> GetActivosAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default);
}