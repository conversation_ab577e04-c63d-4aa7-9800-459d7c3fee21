namespace GestionAPQ_BLZ.Configuration;

using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Nelibur.ObjectMapper;

/// <summary>
/// Configuración de mapeos para TinyMapper
/// </summary>
public static class TinyMapperConfig
{
    public static void Configure()
    {
        // Mapeos de entidades APQLitalsa a DTOs
        TinyMapper.Bind<Nodrizas, NodrizaDTO>();
        TinyMapper.Bind<Operarios, OperarioDTO>();
        TinyMapper.Bind<LotesNodrizas, LoteNodrizaDTO>();
        
        // Mapeos de listas
        TinyMapper.Bind<List<Nodrizas>, List<NodrizaDTO>>();
        TinyMapper.Bind<List<Operarios>, List<OperarioDTO>>();
        TinyMapper.Bind<List<LotesNodrizas>, List<LoteNodrizaDTO>>();
        
        // Mapeos adicionales que puedas necesitar
        // TinyMapper.Bind<OtraEntidad, OtroDTO>();
    }
}
