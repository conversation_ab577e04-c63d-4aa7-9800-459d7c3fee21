namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.Base.APQLitalsa;

public record GetAllNodrizasQuery(bool AsNoTracking = true) : IRequest<ListResult<NodrizaDTO>>;

public class GetAllNodrizasQueryHandler : IRequestHandler<GetAllNodrizasQuery, ListResult<NodrizaDTO>>
{
    private readonly INodrizasRepo _nodrizasRepo;

    public GetAllNodrizasQueryHandler(INodrizasRepo nodrizasRepo)
    {
        _nodrizasRepo = nodrizasRepo;
    }

    public async Task<ListResult<NodrizaDTO>> Handle(GetAllNodrizasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<NodrizaDTO>
        {
            Data = [],
            Errors = []
        };

        try
        {
            var nodrizas = await _nodrizasRepo.GetAllAsync(
                new NodrizasQueryOptions { AsNoTracking = request.AsNoTracking }, 
                cancellationToken);
            
            result.Data = TinyMapper.Map<List<NodrizaDTO>>(nodrizas);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}
