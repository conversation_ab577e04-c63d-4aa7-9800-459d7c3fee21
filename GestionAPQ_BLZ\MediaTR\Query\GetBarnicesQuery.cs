﻿namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.Base.DatoLita01;

public record GetBarnicesQuery(
    string? IdBarniz,
    bool SacarViscosidades,
    QueryOptions QueryOptions)
    : IRequest<ListResult<BarnizDTO>>;

public class GetBarnicesQueryHandler : IRequestHandler<GetBarnicesQuery, ListResult<BarnizDTO>>
{
    private readonly IAArticuRepo _aArticuRepo;
    private readonly IAViscosRepo _aViscosRepo;

    public GetBarnicesQueryHandler(IAArticuRepo aArticuRepo, IAViscosRepo aViscosRepo)
    {
        _aArticuRepo = aArticuRepo;
        _aViscosRepo = aViscosRepo;
    }

    public async Task<ListResult<BarnizDTO>> Handle(GetBarnicesQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<BarnizDTO>
        {
            Data = [],
            Errors = []
        };

        try
        {
            // Usar las QueryOptions pasadas o crear unas por defecto
            result.Data = request.SacarViscosidades
                ? await GetBarnicesConViscosidades(request.IdBarniz, request.QueryOptions, cancellationToken)
                : await GetBarnicesSinViscosidades(request.IdBarniz, request.QueryOptions, cancellationToken);
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private async Task<List<BarnizDTO>> GetBarnicesConViscosidades(string? idBarniz, QueryOptions options,
        CancellationToken cancellationToken)
    {
        var listaBarnices = await _aArticuRepo.GetBarnicesLeftJoinViscosidadesAsync(
            idBarniz,
            _aViscosRepo,
            options,
            cancellationToken);

        return listaBarnices
            .GroupBy(x => x.Item1.Codigo)
            .Select(g => new BarnizDTO
            {
                Articulo = TinyMapper.Map<AArticuDTO>(g.First().Item1),
                ListaViscosidades = g.Where(x => x.Item2 != null)
                    .Select(x => TinyMapper.Map<AViscosDTO>(x.Item2))
                    .ToList()
            }).ToList();
    }

    private async Task<List<BarnizDTO>> GetBarnicesSinViscosidades(string? idBarniz, QueryOptions options,
        CancellationToken cancellationToken)
    {
        var listaBarnices = await _aArticuRepo.GetBarnicesAsync(idBarniz, options, cancellationToken);

        return listaBarnices.Select(barniz => new BarnizDTO
        {
            Articulo = TinyMapper.Map<AArticuDTO>(barniz)
        }).ToList();
    }
}