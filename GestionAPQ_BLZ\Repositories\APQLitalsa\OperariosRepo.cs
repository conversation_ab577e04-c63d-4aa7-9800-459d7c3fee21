namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base.APQLitalsa;
using Extensions;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class OperariosRepo : IOperariosRepo
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<Operarios> _dbSet;

    public OperariosRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<Operarios>();
    }

    // Métodos genéricos usando extensions
    public async Task<List<Operarios>> GetAllAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.GetAllAsync(options, cancellationToken);
    }

    public async Task<List<Operarios>> GetActivosAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.GetWhereAsync(o => o.Activo, options, cancellationToken);
    }

    public async Task<Operarios?> GetByIdAsync(int id, OperariosQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.GetFirstOrDefaultAsync(o => o.Id == id, options, cancellationToken);
    }

    public async Task<int> CountAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(null, options, cancellationToken);
    }

    public async Task<int> CountActivosAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(o => o.Activo, options, cancellationToken);
    }
}
}