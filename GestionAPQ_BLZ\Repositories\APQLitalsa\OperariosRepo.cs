﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class OperariosRepo : IOperariosRepo, IGenericRepo<Operarios>
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<Operarios> _dbSet;

    public OperariosRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<Operarios>();
    }

    public async Task<List<Operarios>> GetAllAsync(OperariosQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.SetOptions(_dbSet);
        return await query.ToListAsync(cancellationToken);
    }
}