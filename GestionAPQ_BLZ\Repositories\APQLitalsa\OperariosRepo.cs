namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class OperariosRepo : Repository<Operarios, APQLitalsaContext>, IOperariosRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public OperariosRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    private IQueryable<Operarios> ApplyQueryOptions(OperariosQueryOptions options)
    {
        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        if (options.IncludeLotes)
            query = query.Include(o => o.LotesNodrizas);

        return query;
    }

    public async Task<Operarios?> GetByIdAsync(int id, OperariosQueryOptions options, CancellationToken cancellationToken = default)
    {
        var query = ApplyQueryOptions(options);

        return await query
            .Where(o => o.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<Operarios>> GetActivosAsync(OperariosQueryOptions options, CancellationToken cancellationToken = default)
    {
        var query = ApplyQueryOptions(options);

        return await query
            .Where(o => o.Activo)
            .ToListAsync(cancellationToken);
    }
}