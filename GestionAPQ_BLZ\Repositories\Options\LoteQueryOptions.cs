namespace GestionAPQ_BLZ.Repositories.Options;

public class LoteQueryOptions
{
    public bool IncludeNodriza { get; set; } = false;
    public bool IncludeOperario { get; set; } = true;  // Por defecto true porque casi siempre se necesita
    public bool AsNoTracking { get; set; } = true;     // Por defecto true por performance
}

public class NodrizaQueryOptions
{
    public bool IncludeLotes { get; set; } = false;    // Incluir todos los lotes de la nodriza
    public bool AsNoTracking { get; set; } = true;     // Por defecto true por performance
}

public class OperarioQueryOptions
{
    public bool IncludeLotes { get; set; } = false;    // Incluir todos los lotes del operario
    public bool AsNoTracking { get; set; } = true;     // Por defecto true por performance
}
