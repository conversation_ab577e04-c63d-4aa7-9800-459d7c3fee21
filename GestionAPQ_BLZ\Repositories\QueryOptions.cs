namespace GestionAPQ_BLZ.Repositories;

/// <summary>
/// Opciones de consulta para repositorios
/// </summary>
public class QueryOptions
{
    /// <summary>
    /// Indica si la consulta debe usar AsNoTracking para mejor performance
    /// </summary>
    public bool AsNoTracking { get; set; } = true;
}

/// <summary>
/// Opciones de consulta para LotesNodrizasRepo
/// </summary>
public class LotesNodrizasQueryOptions : QueryOptions
{
    public bool IncludeNodriza { get; set; } = false;
    public bool IncludeOperario { get; set; } = true;  // Por defecto true porque casi siempre se necesita
}

/// <summary>
/// Opciones de consulta para NodrizasRepo
/// </summary>
public class NodrizasQueryOptions : QueryOptions
{
    public bool IncludeLotes { get; set; } = false;    // Incluir todos los lotes de la nodriza
}

/// <summary>
/// Opciones de consulta para OperariosRepo
/// </summary>
public class OperariosQueryOptions : QueryOptions
{
    public bool IncludeLotes { get; set; } = false;    // Incluir todos los lotes del operario
}
