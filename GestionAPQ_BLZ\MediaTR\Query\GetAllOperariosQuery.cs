namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.Base.APQLitalsa;

public record GetAllOperariosQuery(bool AsNoTracking = true) : IRequest<ListResult<OperarioDTO>>;

public class GetAllOperariosQueryHandler : IRequestHandler<GetAllOperariosQuery, ListResult<OperarioDTO>>
{
    private readonly IOperariosRepo _operariosRepo;

    public GetAllOperariosQueryHandler(IOperariosRepo operariosRepo)
    {
        _operariosRepo = operariosRepo;
    }

    public async Task<ListResult<OperarioDTO>> Handle(GetAllOperariosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<OperarioDTO>
        {
            Data = [],
            Errors = []
        };

        try
        {
            var operarios = await _operariosRepo.GetAllAsync(
                new OperariosQueryOptions { AsNoTracking = request.AsNoTracking }, 
                cancellationToken);
            
            result.Data = TinyMapper.Map<List<OperarioDTO>>(operarios);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}
