namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AViscosRepo : Repository<AViscos, Dato01LitaContext>, IAViscosRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public AViscosRepo(Dato01LitaContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<AViscos>> GetViscosidadesAsync(string? codigo, QueryOptions options, CancellationToken cancellationToken = default)
    {
        var query = GetViscosidadesQuery(codigo, options);
        return await query.ToListAsync(cancellationToken);
    }

    public IQueryable<AViscos> GetViscosidadesQuery(string? codigo, QueryOptions options)
    {
        var query = options.Set(DbSet);

        if (!string.IsNullOrEmpty(codigo))
            query = query.Where(i => i.Codigo.Equals(codigo));

        return query;
    }
}