namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AViscosRepo : IAViscosRepo
{
    private readonly Dato01LitaContext _dbContext;
    private readonly DbSet<AViscos> _dbSet;

    public AViscosRepo(Dato01LitaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<AViscos>();
    }

    public async Task<List<AViscos>> GetViscosidadesAsync(string? codigo, QueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = GetViscosidadesQuery(codigo, options);
        return await query.ToListAsync(cancellationToken);
    }

    public IQueryable<AViscos> GetViscosidadesQuery(string? codigo, QueryOptions options)
    {
        var query = options.Set(_dbSet);

        if (!string.IsNullOrEmpty(codigo))
            query = query.Where(i => i.Codigo.Equals(codigo));

        return query;
    }
}