﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Dato01Lita.Base;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AViscosRepo : IAViscosRepo
{
    private readonly Dato01LitaContext _dbContext;
    private readonly DbSet<AViscos> _dbSet;

    public AViscosRepo(Dato01LitaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<AViscos>();
    }

    public IQueryable<AViscos> GetViscosidadesQuery(string? codigo, QueryOptions options)
    {
        var query = options.SetOptions(_dbSet);

        if (!string.IsNullOrEmpty(codigo))
            query = query.Where(i => i.Codigo.Equals(codigo));

        return query;
    }
}