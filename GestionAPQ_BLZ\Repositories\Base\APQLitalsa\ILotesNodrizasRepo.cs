﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface ILotesNodrizasRepo : IRepository<LotesNodrizas>
{
    public Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProducto(int idNodriza, int idProducto,
        bool asNoTracking, CancellationToken cancellationToken);

    public Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProductoWithNavigation(int idNodriza, int idProducto,
        bool asNoTracking, CancellationToken cancellationToken);
}