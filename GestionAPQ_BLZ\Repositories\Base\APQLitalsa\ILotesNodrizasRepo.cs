﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface ILotesNodrizasRepo : IRepository<LotesNodrizas>
{
    Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProductoAsync(
        int idNodriza,
        int idProducto,
        LotesNodrizasQueryOptions options,  // ✅ Obligatorio, sin default
        CancellationToken cancellationToken = default);
}