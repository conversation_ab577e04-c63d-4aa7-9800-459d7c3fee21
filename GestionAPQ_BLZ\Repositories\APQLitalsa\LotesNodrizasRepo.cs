﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class LotesNodrizasRepo : Repository<LotesNodrizas, APQLitalsaContext>, ILotesNodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public LotesNodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProductoAsync(
        int idNodriza,
        int idProducto,
        LotesNodrizasQueryOptions options,  // ✅ Obligatorio
        CancellationToken cancellationToken = default)
    {
        var query = options.AsNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        // Includes condicionales basados en options
        if (options.IncludeNodriza)
            query = query.Include(l => l.IdNodrizaNavigation);

        if (options.IncludeOperario)
            query = query.Include(l => l.IdOperarioNavigation);

        return await query
            .Where(l => l.IdNodriza.HasValue
                        && l.IdNodriza == idNodriza
                        && l.IdProducto == idProducto)
            .OrderByDescending(l => l.Fecha)  // Consistente con Fecha
            .FirstOrDefaultAsync(cancellationToken);
    }
}