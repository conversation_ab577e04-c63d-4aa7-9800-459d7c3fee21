﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class LotesNodrizasRepo : ILotesNodrizasRepo
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<LotesNodrizas> _dbSet;

    public LotesNodrizasRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<LotesNodrizas>();
    }

    public async Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProductoAsync(
        int idNodriza,
        int idProducto,
        LotesNodrizasQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.Set(_dbSet);

        return await query
            .Where(l => l.IdNodriza.HasValue
                        && l.IdNodriza == idNodriza
                        && l.IdProducto == idProducto)
            .OrderByDescending(l => l.Fecha)
            .FirstOrDefaultAsync(cancellationToken);
    }
}