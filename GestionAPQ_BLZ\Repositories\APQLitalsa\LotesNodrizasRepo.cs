﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class LotesNodrizasRepo : Repository<LotesNodrizas, APQLitalsaContext>, ILotesNodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public LotesNodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProducto(int idNodriza, int idProducto, bool asNoTracking,
        CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken,
            l => l.IdNodriza.HasValue
                 && l.IdNodriza == idNodriza
                 && l.IdProducto == idProducto,
            orderByDesc: l => l.Id);
    }

    public async Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProductoWithNavigation(int idNodriza, int idProducto,
        bool asNoTracking, CancellationToken cancellationToken)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        return await query
            .Include(l => l.IdNodrizaNavigation)
            .Include(l => l.IdOperarioNavigation)
            .Where(l => l.IdNodriza.HasValue
                        && l.IdNodriza == idNodriza
                        && l.IdProducto == idProducto)
            .OrderByDescending(l => l.Fecha)
            .FirstOrDefaultAsync(cancellationToken);
    }
}