﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class LotesNodrizasRepo : Repository<LotesNodrizas, APQLitalsaContext>, ILotesNodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public LotesNodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }
}