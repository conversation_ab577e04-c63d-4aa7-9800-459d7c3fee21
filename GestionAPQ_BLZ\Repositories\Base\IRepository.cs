﻿namespace GestionAPQ_BLZ.Repositories.Base;

using System.Linq.Expressions;

public interface IRepository<TEntity> where TEntity : class
{
    // Métodos de escritura
    void AddEntity(TEntity entity);
    void AddRangeEntities(IEnumerable<TEntity> entities);
    void UpdateEntity(TEntity entity);
    void DeleteEntity(TEntity entity);
    void DeleteRangeEntities(IEnumerable<TEntity> entities);
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    // Método de lectura usado por GetAllEntitiesQueryHandler
    Task<List<TEntity>> GetAllAsync(
        bool asNoTracking = true,
        CancellationToken cancellationToken = default,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null);
}