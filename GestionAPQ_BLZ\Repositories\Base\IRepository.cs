﻿namespace GestionAPQ_BLZ.Repositories.Base;

using System.Linq.Expressions;

public interface IRepository<TEntity> where TEntity : class
{
    void AddEntity(TEntity entity);
    void AddRangeEntities(IEnumerable<TEntity> entities);
    void UpdateEntity(TEntity entity);
    void DeleteEntity(TEntity entity);
    void DeleteRangeEntities(IEnumerable<TEntity> entities);
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    Task<List<TEntity>> GetAllAsync(
        bool asNoTracking = true,
        CancellationToken cancellationToken = default,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null);

    Task<List<TEntity>> GetListAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null);

    Task<TEntity?> GetFirstOrDefaultAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null);

    Task<TEntity?> GetLastOrDefaultAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null);

    Task<TEntity?> GetFirstOrDefaultWithIncludesAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        params Expression<Func<TEntity, object>>[] includes);

    Task<List<TEntity>> GetListWithIncludesAsync(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        params Expression<Func<TEntity, object>>[] includes);

    Task<int> CountAsync(
        Expression<Func<TEntity, bool>>? where = null,
        CancellationToken cancellationToken = default);

    Task<bool> AnyAsync(
        Expression<Func<TEntity, bool>>? where = null,
        CancellationToken cancellationToken = default);

    Task<List<TEntity>> GetPagedAsync(
        int page,
        int pageSize,
        bool asNoTracking = true,
        Expression<Func<TEntity, bool>>? where = null,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        CancellationToken cancellationToken = default);
}