namespace GestionAPQ_BLZ.Repositories.Base;

using System.Linq.Expressions;

public interface IRepository<TEntity> where TEntity : class
{
    public Task<int> UpdateEntityWithId(CancellationToken cancellationToken, TEntity entity);
    public Task<int> DeleteEntity(CancellationToken cancellationToken, TEntity entity);

    public Task<List<TEntity>> GetAll(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null);

    public Task<List<TEntity>> GetList(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null);

    public Task<TEntity?> GetFirstOrDefault(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null);

    public Task<TEntity?> GetLastOrDefault(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, object>>? orderBy = null,
        Expression<Func<TEntity, object>>? orderByDesc = null,
        Expression<Func<TEntity, object>>? groupBy = null);

    public Task<TEntity?> GetFirstOrDefaultWithIncludesAsync<TProperty>(
        bool asNoTracking,
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>> where,
        Expression<Func<TEntity, TProperty>> include,
        Expression<Func<TEntity, object>>? orderByDesc = null);
}