namespace GestionAPQ_BLZ.Repositories.Base.DatoLita01;

using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;

public interface IAViscosRepo : IRepository<AViscos>
{
    Task<List<AViscos>> GetViscosidadesAsync(string? codigo, QueryOptions options, CancellationToken cancellationToken = default);
    Task<AViscos?> GetViscosidadAsync(string codigo, QueryOptions options, CancellationToken cancellationToken = default);
}