namespace GestionAPQ_BLZ.Repositories;

/// <summary>
/// Ejemplos de uso de QueryOptions con Factory Pattern y Extension Methods
/// Este archivo es solo para documentación y ejemplos
/// </summary>
public static class QueryOptionsExamples
{
    // ✅ EJEMPLOS DE FACTORY PATTERN
    public static class FactoryPatternExamples
    {
        public static async Task EjemplosNodrizas(INodrizasRepo repo)
        {
            // ✅ Consulta básica sin lotes
            var nodriza1 = await repo.GetByIdAsync(1, 
                QueryOptionsFactory.ForNodrizasBasic());

            // ✅ Consulta con lotes incluidos
            var nodriza2 = await repo.GetByIdAsync(2, 
                QueryOptionsFactory.ForNodrizasWithLotes());

            // ✅ Consulta personalizada
            var nodriza3 = await repo.GetByIdAsync(3, 
                QueryOptionsFactory.ForNodrizas(includeLotes: true, asNoTracking: false));
        }

        public static async Task EjemplosLotes(ILotesNodrizasRepo repo)
        {
            // ✅ Consulta básica (solo datos del lote)
            var lote1 = await repo.GetUltimoLoteByNodrizaYProductoAsync(1, 100,
                QueryOptionsFactory.ForLotesBasic());

            // ✅ Consulta con operario incluido
            var lote2 = await repo.GetUltimoLoteByNodrizaYProductoAsync(2, 200,
                QueryOptionsFactory.ForLotesWithOperario());

            // ✅ Consulta completa (con nodriza y operario)
            var lote3 = await repo.GetUltimoLoteByNodrizaYProductoAsync(3, 300,
                QueryOptionsFactory.ForLotesComplete());
        }

        public static async Task EjemplosBarnices(IAArticuRepo repo)
        {
            // ✅ Consulta de barnices
            var barnices = await repo.GetBarnicesAsync("B001",
                QueryOptionsFactory.ForBarnices());

            // ✅ Consulta específica de un barniz
            var barniz = await repo.GetBarnizAsync("B001",
                QueryOptionsFactory.ForArticulos(asNoTracking: false)); // Para modificar
        }
    }

    // ✅ EJEMPLOS DE EXTENSION METHODS
    public static class ExtensionMethodsExamples
    {
        public static async Task EjemplosNodrizas(INodrizasRepo repo)
        {
            // ✅ Fluent interface básico
            var nodriza1 = await repo.GetByIdAsync(1,
                new NodrizasQueryOptions().WithoutLotes().WithNoTracking());

            // ✅ Fluent interface con lotes
            var nodriza2 = await repo.GetByIdAsync(2,
                new NodrizasQueryOptions().WithLotes().WithNoTracking());

            // ✅ Para modificaciones
            var nodriza3 = await repo.GetByIdAsync(3,
                new NodrizasQueryOptions().WithLotes().WithTracking());
        }

        public static async Task EjemplosLotes(ILotesNodrizasRepo repo)
        {
            // ✅ Solo operario
            var lote1 = await repo.GetUltimoLoteByNodrizaYProductoAsync(1, 100,
                new LotesNodrizasQueryOptions().WithOperario().WithoutNodriza());

            // ✅ Solo nodriza
            var lote2 = await repo.GetUltimoLoteByNodrizaYProductoAsync(2, 200,
                new LotesNodrizasQueryOptions().WithNodriza().WithoutOperario());

            // ✅ Todo incluido
            var lote3 = await repo.GetUltimoLoteByNodrizaYProductoAsync(3, 300,
                new LotesNodrizasQueryOptions().WithAllIncludes().WithNoTracking());

            // ✅ Nada incluido
            var lote4 = await repo.GetUltimoLoteByNodrizaYProductoAsync(4, 400,
                new LotesNodrizasQueryOptions().WithoutIncludes().WithNoTracking());
        }
    }

    // ✅ EJEMPLOS COMBINADOS (Factory + Extensions)
    public static class CombinedExamples
    {
        public static async Task EjemplosCombinados(INodrizasRepo nodrizasRepo, ILotesNodrizasRepo lotesRepo)
        {
            // ✅ Factory como base + Extensions para personalizar
            var nodriza = await nodrizasRepo.GetByIdAsync(1,
                QueryOptionsFactory.ForNodrizasBasic().WithTracking()); // Para modificar

            // ✅ Factory específico + Extension para cambiar tracking
            var lote = await lotesRepo.GetUltimoLoteByNodrizaYProductoAsync(1, 100,
                QueryOptionsFactory.ForLotesComplete().WithTracking()); // Para modificar
        }
    }

    // ✅ EJEMPLOS DE CASOS DE USO REALES
    public static class RealWorldExamples
    {
        public static async Task CasoDeUso_ConsultaSoloLectura(INodrizasRepo repo)
        {
            // ✅ Para mostrar en UI (solo lectura, con performance)
            var nodrizas = await repo.GetActivasAsync(
                QueryOptionsFactory.ForNodrizasBasic()); // AsNoTracking = true por defecto
        }

        public static async Task CasoDeUso_ModificarDatos(INodrizasRepo repo)
        {
            // ✅ Para modificar datos (necesita tracking)
            var nodriza = await repo.GetByIdAsync(1,
                QueryOptionsFactory.ForNodrizas(asNoTracking: false));

            // Modificar la nodriza...
            repo.UpdateEntity(nodriza);
            await repo.SaveChangesAsync();
        }

        public static async Task CasoDeUso_ReporteCompleto(ILotesNodrizasRepo repo)
        {
            // ✅ Para reportes que necesitan toda la información
            var loteCompleto = await repo.GetUltimoLoteByNodrizaYProductoAsync(1, 100,
                QueryOptionsFactory.ForLotesComplete()); // Incluye nodriza y operario
        }

        public static async Task CasoDeUso_ListadoRapido(ILotesNodrizasRepo repo)
        {
            // ✅ Para listados rápidos sin información extra
            var loteBasico = await repo.GetUltimoLoteByNodrizaYProductoAsync(1, 100,
                QueryOptionsFactory.ForLotesBasic()); // Solo datos del lote
        }
    }

    // ✅ COMPARACIÓN: ANTES vs DESPUÉS
    public static class BeforeAfterComparison
    {
        public static async Task Antes_SinPatrones(INodrizasRepo repo, ILotesNodrizasRepo lotesRepo)
        {
            // ❌ ANTES: Repetitivo y propenso a errores
            var nodriza = await repo.GetByIdAsync(1, new NodrizasQueryOptions 
            { 
                IncludeLotes = false, 
                AsNoTracking = true 
            });

            var lote = await lotesRepo.GetUltimoLoteByNodrizaYProductoAsync(1, 100, 
                new LotesNodrizasQueryOptions 
                { 
                    IncludeNodriza = false, 
                    IncludeOperario = true, 
                    AsNoTracking = true 
                });
        }

        public static async Task Despues_ConPatrones(INodrizasRepo repo, ILotesNodrizasRepo lotesRepo)
        {
            // ✅ DESPUÉS: Limpio y expresivo
            var nodriza = await repo.GetByIdAsync(1, 
                QueryOptionsFactory.ForNodrizasBasic());

            var lote = await lotesRepo.GetUltimoLoteByNodrizaYProductoAsync(1, 100,
                QueryOptionsFactory.ForLotesWithOperario());

            // ✅ O con Extensions para más control
            var lotePersonalizado = await lotesRepo.GetUltimoLoteByNodrizaYProductoAsync(1, 100,
                new LotesNodrizasQueryOptions().WithOperario().WithoutNodriza().WithNoTracking());
        }
    }
}
