namespace GestionAPQ_BLZ.Repositories;

/// <summary>
/// Extension methods para hacer más fluido el uso de QueryOptions
/// </summary>
public static class QueryOptionsExtensions
{
    #region NodrizasQueryOptions Extensions

    public static NodrizasQueryOptions WithLotes(this NodrizasQueryOptions options, bool include = true)
    {
        options.IncludeLotes = include;
        return options;
    }

    public static NodrizasQueryOptions WithoutLotes(this NodrizasQueryOptions options)
    {
        options.IncludeLotes = false;
        return options;
    }

    #endregion

    #region OperariosQueryOptions Extensions

    public static OperariosQueryOptions WithLotes(this OperariosQueryOptions options, bool include = true)
    {
        options.IncludeLotes = include;
        return options;
    }

    public static OperariosQueryOptions WithoutLotes(this OperariosQueryOptions options)
    {
        options.IncludeLotes = false;
        return options;
    }

    #endregion

    #region LotesNodrizasQueryOptions Extensions

    public static LotesNodrizasQueryOptions WithNodriza(this LotesNodrizasQueryOptions options, bool include = true)
    {
        options.IncludeNodriza = include;
        return options;
    }

    public static LotesNodrizasQueryOptions WithOperario(this LotesNodrizasQueryOptions options, bool include = true)
    {
        options.IncludeOperario = include;
        return options;
    }

    public static LotesNodrizasQueryOptions WithoutNodriza(this LotesNodrizasQueryOptions options)
    {
        options.IncludeNodriza = false;
        return options;
    }

    public static LotesNodrizasQueryOptions WithoutOperario(this LotesNodrizasQueryOptions options)
    {
        options.IncludeOperario = false;
        return options;
    }

    #endregion

    #region QueryOptions Base Extensions

    public static T WithNoTracking<T>(this T options) where T : QueryOptions
    {
        options.AsNoTracking = true;
        return options;
    }

    public static T WithTracking<T>(this T options) where T : QueryOptions
    {
        options.AsNoTracking = false;
        return options;
    }

    #endregion
}
