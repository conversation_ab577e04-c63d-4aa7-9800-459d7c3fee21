namespace GestionAPQ_BLZ.Repositories;

/// <summary>
/// Extension methods para hacer más fluido el uso de QueryOptions
/// </summary>
public static class QueryOptionsExtensions
{
    #region NodrizasQueryOptions Extensions
    
    /// <summary>
    /// Configura la consulta para incluir los lotes de la nodriza
    /// </summary>
    public static NodrizasQueryOptions WithLotes(this NodrizasQueryOptions options)
    {
        options.IncludeLotes = true;
        return options;
    }
    
    /// <summary>
    /// Configura la consulta para NO incluir los lotes de la nodriza
    /// </summary>
    public static NodrizasQueryOptions WithoutLotes(this NodrizasQueryOptions options)
    {
        options.IncludeLotes = false;
        return options;
    }
    
    #endregion

    #region OperariosQueryOptions Extensions
    
    /// <summary>
    /// Configura la consulta para incluir los lotes del operario
    /// </summary>
    public static OperariosQueryOptions WithLotes(this OperariosQueryOptions options)
    {
        options.IncludeLotes = true;
        return options;
    }
    
    /// <summary>
    /// Configura la consulta para NO incluir los lotes del operario
    /// </summary>
    public static OperariosQueryOptions WithoutLotes(this OperariosQueryOptions options)
    {
        options.IncludeLotes = false;
        return options;
    }
    
    #endregion

    #region LotesNodrizasQueryOptions Extensions
    
    /// <summary>
    /// Configura la consulta para incluir la información de la nodriza
    /// </summary>
    public static LotesNodrizasQueryOptions WithNodriza(this LotesNodrizasQueryOptions options)
    {
        options.IncludeNodriza = true;
        return options;
    }
    
    /// <summary>
    /// Configura la consulta para incluir la información del operario
    /// </summary>
    public static LotesNodrizasQueryOptions WithOperario(this LotesNodrizasQueryOptions options)
    {
        options.IncludeOperario = true;
        return options;
    }
    
    /// <summary>
    /// Configura la consulta para NO incluir la información de la nodriza
    /// </summary>
    public static LotesNodrizasQueryOptions WithoutNodriza(this LotesNodrizasQueryOptions options)
    {
        options.IncludeNodriza = false;
        return options;
    }
    
    /// <summary>
    /// Configura la consulta para NO incluir la información del operario
    /// </summary>
    public static LotesNodrizasQueryOptions WithoutOperario(this LotesNodrizasQueryOptions options)
    {
        options.IncludeOperario = false;
        return options;
    }
    
    /// <summary>
    /// Configura la consulta para incluir toda la información relacionada
    /// </summary>
    public static LotesNodrizasQueryOptions WithAllIncludes(this LotesNodrizasQueryOptions options)
    {
        options.IncludeNodriza = true;
        options.IncludeOperario = true;
        return options;
    }
    
    /// <summary>
    /// Configura la consulta para NO incluir información relacionada
    /// </summary>
    public static LotesNodrizasQueryOptions WithoutIncludes(this LotesNodrizasQueryOptions options)
    {
        options.IncludeNodriza = false;
        options.IncludeOperario = false;
        return options;
    }
    
    #endregion

    #region QueryOptions Base Extensions
    
    /// <summary>
    /// Configura la consulta para usar AsNoTracking (mejor performance para solo lectura)
    /// </summary>
    public static T WithNoTracking<T>(this T options) where T : QueryOptions
    {
        options.AsNoTracking = true;
        return options;
    }
    
    /// <summary>
    /// Configura la consulta para usar tracking (necesario para modificaciones)
    /// </summary>
    public static T WithTracking<T>(this T options) where T : QueryOptions
    {
        options.AsNoTracking = false;
        return options;
    }
    
    #endregion
}
