﻿namespace GestionAPQ_BLZ;

using Blazr.RenderState.Server;
using Common.ResponseModels.ResponseModels;
using DevExpress.AspNetCore.Reporting;
using DevExpress.Blazor;
using DevExpress.Blazor.Reporting;
using DevExpress.XtraReports.Services;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using MediatR;
using MediaTR.Query;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using Repositories.APQLitalsa;
using Repositories.Base;
using Repositories.Base.APQLitalsa;
using Repositories.Base.DatoLita01;
using Repositories.DatoLita01;
using Services;
using Services.Base;

public static class DependencyInjection
{
    public static WebApplicationBuilder CompleteWebUiConfig(this WebApplicationBuilder builder)
    {
        // CONVENCIÓN BLAZOR
        builder.Services.AddRazorComponents()
            .AddInteractiveServerComponents()
            .AddInteractiveWebAssemblyComponents();

        // BLAZRRENDERSTATE
        builder.AddBlazrRenderStateServerServices();


        // DEVEXPRESS
        builder.Services.AddDevExpressBlazor(options =>
        {
            options.BootstrapVersion = BootstrapVersion.v5;
            options.SizeMode = SizeMode.Medium;
        });
        builder.Services.AddDevExpressBlazorReporting();
        builder.Services.ConfigureReportingServices(configurator =>
        {
            configurator.ConfigureReportDesigner(designerConfigurator => { });
            configurator.UseAsyncEngine();
        });
        builder.Services.AddRazorPages(); // necesario tmbn para que funcione dxdocumentviewer
        builder.Services.AddScoped<IReportProviderAsync, CustomReportProviderAsync>();


        // SCOPES
        builder.Services.AddScoped<ILocalStorageService, LocalStorageService>();
        builder.Services.AddScoped<ICustomToastService, CustomToastService>();
        builder.Services.AddScoped<ICustomDialogService, CustomDialogService>();
        builder.Services.AddScoped<IDocumentService, DocumentService>();

        return builder;
    }

    public static WebApplicationBuilder CompleteApplicationConfig(this WebApplicationBuilder builder)
    {
        // TINYMAPPER
        SetMapeos();

        // MEDIATR
        builder.Services.AddMediatR(
            cfg => { cfg.RegisterServicesFromAssemblies(typeof(DependencyInjection).Assembly); });

        // HANDLERS GENÉRICOS ESPECÍFICOS
        builder.Services
            .AddScoped<IRequestHandler<GetAllEntitiesQuery<Operarios, OperarioDTO>,
                    ListResult<OperarioDTO>>,
                GetAllEntitiesQueryHandler<Operarios, OperarioDTO>>();
        builder.Services
            .AddScoped<IRequestHandler<GetAllEntitiesQuery<Nodrizas, NodrizaDTO>,
                    ListResult<NodrizaDTO>>,
                GetAllEntitiesQueryHandler<Nodrizas, NodrizaDTO>>();

        return builder;
    }

    public static WebApplicationBuilder CompleteInfraestructureConfig(this WebApplicationBuilder builder)
    {
        // IN2CONTEXT
        builder.Services.AddDbContext<Dato01LitaContext>(options =>
        {
            options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionDatoLita01"));
        });
        builder.Services.AddScoped<IAArticuRepo, AArticuRepo>();
        builder.Services.AddScoped<IAViscosRepo, AViscosRepo>();

        // APQLITALSACONTEXT
        builder.Services.AddDbContext<APQLitalsaContext>(options =>
        {
            options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionApqLitalsa"));
        });
        builder.Services.AddScoped<IOperariosRepo, OperariosRepo>();
        builder.Services.AddScoped<INodrizasRepo, NodrizasRepo>();
        builder.Services.AddScoped<ILotesNodrizasRepo, LotesNodrizasRepo>();

        // PARA USAR QUERY GENÉRICA
        builder.Services.AddScoped<IRepository<Operarios>>(provider =>
            provider.GetRequiredService<IOperariosRepo>());
        builder.Services.AddScoped<IRepository<Nodrizas>>(provider =>
            provider.GetRequiredService<INodrizasRepo>());

        return builder;
    }

    private static void SetMapeos()
    {
        SetMapeoEstandar<Operarios, OperarioDTO>();
        SetMapeoEstandar<Nodrizas, NodrizaDTO>();
        SetMapeoEstandar<LotesNodrizas, LoteNodrizaDTO>();
        SetMapeoEstandar<AArticu, AArticuDTO>();
        SetMapeoEstandar<AViscos, AViscosDTO>();
    }

    private static void SetMapeoEstandar<TEntity, TDto>()
    {
        TinyMapper.Bind<TEntity, TDto>();
        TinyMapper.Bind<TDto, TEntity>();
        TinyMapper.Bind<List<TEntity>, List<TDto>>();
        TinyMapper.Bind<List<TDto>, List<TEntity>>();
    }
}