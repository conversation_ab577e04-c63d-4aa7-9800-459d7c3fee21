﻿namespace GestionAPQ_BLZ.Repositories.Dato01Lita.Base;

using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;

public interface IAArticuRepo : IRepository<AArticu>
{
    Task<List<AArticu>> GetBarnicesAsync(string? idBarniz, QueryOptions options,
        CancellationToken cancellationToken = default);

    Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidadesAsync(string? idBarniz, IAViscosRepo aViscosRepo,
        QueryOptions options, CancellationToken cancellationToken = default);
}