namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.Base.APQLitalsa;

public record GetDetallesNodrizaQuery(
    int IdNodriza,
    NodrizasQueryOptions QueryNodrizasOptions,
    LotesNodrizasQueryOptions QueryLotesOptions)
    : IRequest<SingleResult<DetalleNodrizaDTO>>;

public class
    GetDetallesNodrizaQueryHandler : IRequestHandler<GetDetallesNodrizaQuery,
    SingleResult<DetalleNodrizaDTO>>
{
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;
    private readonly INodrizasRepo _nodrizasRepo;

    public GetDetallesNodrizaQueryHandler(INodrizasRepo nodrizasRepo, ILotesNodrizasRepo lotesNodrizasRepo)
    {
        _nodrizasRepo = nodrizasRepo;
        _lotesNodrizasRepo = lotesNodrizasRepo;
    }

    public async Task<SingleResult<DetalleNodrizaDTO>> Handle(
        GetDetallesNodrizaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<DetalleNodrizaDTO>
        {
            Data = null,
            Errors = []
        };

        try
        {
            // Usar las QueryOptions pasadas o crear unas por defecto
            var nodrizasOptions = request.QueryNodrizasOptions ?? new NodrizasQueryOptions();
            var lotesOptions = request.QueryLotesOptions ?? new LotesNodrizasQueryOptions { IncludeOperario = true };

            // Paso 1: Obtener solo la nodriza
            var nodriza = await _nodrizasRepo.GetByIdAsync(request.IdNodriza, nodrizasOptions, cancellationToken);

            if (nodriza == null)
            {
                result.Errors.Add($"No se ha encontrado la nodriza con ID {request.IdNodriza}.");
                return result;
            }

            if (nodriza.IdProducto == null)
            {
                result.Errors.Add($"La nodriza {nodriza.NumNodriza} no tiene un producto asignado.");
                return result;
            }

            // Paso 2: Obtener SOLO el último lote específico con navigation properties
            var ultimoLote = await _lotesNodrizasRepo.GetUltimoLoteByNodrizaYProductoAsync(
                nodriza.Id,
                nodriza.IdProducto.Value,
                lotesOptions,
                cancellationToken);

            // Paso 3: Mapear a DTOs
            var nodrizaDto = TinyMapper.Map<NodrizaDTO>(nodriza);
            var ultimoLoteDto = ultimoLote != null ? TinyMapper.Map<LoteNodrizaDTO>(ultimoLote) : null;

            result.Data = new DetalleNodrizaDTO
            {
                Nodriza = nodrizaDto,
                UltimoLote = ultimoLoteDto
            };
        }
        catch (Exception ex)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}