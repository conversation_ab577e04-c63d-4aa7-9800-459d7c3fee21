namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public record GetDetallesNodrizaQuery(int idNodriza)
    : IRequest<SingleResult<DetalleNodrizaDTO>>;

public class
    GetDetallesNodrizaQueryHandler : IRequestHandler<GetDetallesNodrizaQuery,
    SingleResult<DetalleNodrizaDTO>>
{
    private readonly INodrizasRepo _nodrizasRepo;

    public GetDetallesNodrizaQueryHandler(INodrizasRepo nodrizasRepo)
    {
        _nodrizasRepo = nodrizasRepo;
    }

    public async Task<SingleResult<DetalleNodrizaDTO>> Handle(
        GetDetallesNodrizaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<DetalleNodrizaDTO>
        {
            Data = null,
            Errors = []
        };

        try
        {
            // Paso 1: Obtener la nodriza con sus lotes incluidos usando navigation properties
            var nodriza = await _nodrizasRepo.GetNodrizaWithLotes(request.idNodriza, true, cancellationToken);

            if (nodriza == null)
            {
                result.Errors.Add($"No se ha encontrado la nodriza con ID {request.idNodriza}.");
                return result;
            }

            if (nodriza.IdProducto == null)
            {
                result.Errors.Add($"La nodriza {nodriza.NumNodriza} no tiene un producto asignado.");
                return result;
            }

            // Paso 2: Usar navigation properties para obtener el último lote por IdProducto
            var ultimoLote = nodriza.LotesNodrizas
                .Where(l => l.IdProducto == nodriza.IdProducto.Value)
                .OrderByDescending(l => l.Fecha)
                .FirstOrDefault();

            // Paso 3: Mapear a DTOs
            var nodrizaDto = TinyMapper.Map<NodrizaDTO>(nodriza);
            var ultimoLoteDto = ultimoLote != null ? TinyMapper.Map<LoteNodrizaDTO>(ultimoLote) : null;

            result.Data = new DetalleNodrizaDTO
            {
                Nodriza = nodrizaDto,
                UltimoLote = ultimoLoteDto
            };
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Error al obtener los detalles de la nodriza: {ex.Message}");
        }

        return result;
    }
}