﻿namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public record GetDetallesNodrizaQuery(int idNodriza)
    : IRequest<SingleResult<DetalleNodrizaDTO>>;

public class
    GetDetallesNodrizaQueryHandler : IRequestHandler<GetDetallesNodrizaQuery,
    SingleResult<DetalleNodrizaDTO>>
{
    private readonly INodrizasRepo _nodrizasRepo;
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;

    public GetDetallesNodrizaQueryHandler(INodrizasRepo nodrizasRepo,
        ILotesNodrizasRepo lotesNodrizasRepo)
    {
        _nodrizasRepo = nodrizasRepo;
        _lotesNodrizasRepo = lotesNodrizasRepo;
    }

    public async Task<SingleResult<DetalleNodrizaDTO>> Handle(
        GetDetallesNodrizaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<DetalleNodrizaDTO>
        {
            Data = null,
            Errors = []
        };

        // query para sacar el producto de la nodriza
        var tablaProductoNodriza =
            await _tablaProductosNodrizasRepo.GetTablaProductosNodrizasByNumNodriza(request.NumNodriza,
                true, cancellationToken);
        var idProducto = tablaProductoNodriza?.Idproducto ?? null;
        if (idProducto == null)
        {
            result.Errors.Add($"No se ha encontrado el barniz de la nodriza {request.NumNodriza}.");
            return result;
        }

        // query para sacar el último lote del barniz
        var ultimoLoteNodriza = await _nodrizasRepo.GetUltimoLoteNodrizaByIdProducto(
            request.NumNodriza,
            idProducto.Value, true, cancellationToken);

        // cargamos data en result
        var tablaProductoNodrizaDto = TinyMapper.Map<TablaProductosNodrizasDTO>(tablaProductoNodriza);
        var ultimoLoteNodrizaDto =
            ultimoLoteNodriza != null ? TinyMapper.Map<LotesNodrizasDTO>(ultimoLoteNodriza) : null;
        result.Data = new DetalleNodrizaDTO
        {
            TablaProductoNodriza = tablaProductoNodrizaDto,
            UltimoLote = ultimoLoteNodrizaDto
        };

        return result;
    }
}